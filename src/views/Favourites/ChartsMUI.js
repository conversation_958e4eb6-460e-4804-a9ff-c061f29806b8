import React from 'react';
import { Paper, Divider, IconButton, Grid, Box } from '@material-ui/core';
import { withStyles } from '@material-ui/styles';
import Tabs from '@material-ui/core/Tabs';
import Tab from '@material-ui/core/Tab';
import StarBorderIcon from '@material-ui/icons/StarBorder';
import DashboardLineRenderer from 'src/components/charts/DashboardLineRenderer';
import 'src/styles.css';
import {
  isBar<PERSON><PERSON>,
  isWormix<PERSON><PERSON>,
  isTech<PERSON>hart,
  isAdvisor<PERSON>hart,
  isTechComparison<PERSON>hart,
  isAdvisorComparisonChart,
  isAdvisorOpcategory<PERSON>hart,
  isMonthComparisonChart,
  isDiscountComparisonApexChart,
  isPartsComparison<PERSON>hart,
  isAddonsComparisonChart,
  isOpportunityChart
} from '../../utils/Utils';

import DashboardBarRenderer from 'src/components/charts/DashboardBarRenderer';
import WorkMixRenderer from 'src/views/AnalyzeData/WorkMixCharts/BarChartRenderer';
import TechnicianRenderP from 'src/views/AnalyzeData/TechEfficiency/BarChartRendererPostgraphile';
import { getChartName } from 'src/components/ViewGraphDetailsAction';
import AdvisorPerformanceRenderer from 'src/views/AnalyzeData/ServiceAdvisorEfficiency/AdvisorChartRenderer';
import TechnicianMonthComparison from 'src/views/AnalyzeData/TechEfficiency/ColumnRenderer';
import AdvisorComparisonCharts from 'src/views/AnalyzeData/ServiceAdvisorEfficiency/ColumnRenderer';
import AdvisorOpcategoryCharts from 'src/views/AnalyzeData/ServiceAdvisorEfficiency/OpCategoryChartRenderer';
import HighCharts from 'src/views/AnalyzeData/ComparisonCharts/ColumnRenderer';
import ApexChartsDiscount from 'src/views/Discounts/DiscountColumnRenderer';
import ApexCharts from 'src/views/AddOns/ServiceAdvisor/ColumnRenderer';
import OpportunityCharts from 'src/components/charts/OpportunityCharts';
import clsx from 'clsx';
import PageHeader from 'src/components/PageHeader';

var lodash = require('lodash');
 class ChartsMUI extends React.PureComponent {
  constructor(props) {
    super(props);

    this.state = {
      reloadCounter: 0,
      timePassed: false,
      favouriteData: [],
      filters: 2,
      isLoading: false,
      checkEmpty: false
    };
  }

  componentDidMount() {
    this.setResetDashboard();
  }

  setFilterCharts = value => {
    if (value) {
      this.setState({
        filters: value
      });
    }
    return this.state.filters;
  };

  removeFav = value => {
    if (value == 2) {
      this.setState({
        isLoading: true
      });
    } else if (value == 3) {
      this.setState({
        isLoading: false
      });
    }
    this.setResetDashboard();
  };

  setResetDashboard = () => {
    this.setState({
      reloadCounter: this.state.reloadCounter + 1
    });
  };

  handleChange = value => {
    this.setState({
      checkEmpty: value
    });
  };

  handleChartPopup = () => {
    // Chart popup handler
  };

  handleClose = () => {
    // Close handler
  };

  getHighlighedDiv = () => {
    // Highlight handler
  };

  renderChart = (value) => {
    if (isBarChart(value) == false &&
        isWormixChart(value) == false &&
        isTechChart(value) == false &&
        isAdvisorChart(value) == false &&
        isTechComparisonChart(value) == false &&
        isAdvisorComparisonChart(value) == false &&
        isAdvisorOpcategoryChart(value) == false &&
        isMonthComparisonChart(value) == false &&
        isDiscountComparisonApexChart(value) == false &&
        isPartsComparisonChart(value) == false &&
        isAddonsComparisonChart(value) == false &&
        isOpportunityChart(value) == false) {
      return (
        <DashboardLineRenderer
          filterCharts={this.state.filters}
          removeFavourite={this.removeFav}
          handleChartPopup={this.handleChartPopup}
          handleClosePopup={this.handleClose}
          handleClose={this.handleClose}
          chartId={Number(value)}
          isFrom={'source_page'}
          headerClick={this.getHighlighedDiv}
          chartData={[]}
          isDataLoaded={false}
        />
      );
    } else if (isWormixChart(value)) {
      return (
        <WorkMixRenderer
          removeFav={this.removeFav}
          chartId={Number(value)}
          handleChartPopup={this.handleChartPopup}
          handleClosePopup={this.handleClose}
          handleClose={this.handleClose}
          isFrom={'source_page'}
        />
      );
    } else if (isTechChart(value)) {
      return (
        <TechnicianRenderP
          removeFav={this.removeFav}
          chartId={Number(value)}
          title={value}
          handleClose={this.handleClose}
          handleClosePopup={this.handleClose}
          tech={
            localStorage.getItem('selectedTech') == undefined
              ? 'All'
              : localStorage.getItem('selectedTech')
          }
          techName={localStorage.getItem('selectedTechName')}
          checkEmpty={this.handleChange}
          callFrom={0}
          handleChartPopup={this.handleChartPopup}
          chartTitle={getChartName(value)}
          isFrom={'source_page'}
          session={this.props.session}
        />
      );
    } else if (isAdvisorChart(value)) {
      return (
        <AdvisorPerformanceRenderer
          removeFav={this.removeFav}
          chartId={Number(value)}
          title={1}
          handleClosePopup={this.handleClose}
          handleClose={this.handleClose}
          handleChartPopup={this.handleChartPopup}
          advisor={localStorage.getItem('selectedAdvisor')}
          advisorName={['All']}
          serviceAdvisor={this.props.session.serviceAdvisor}
          checkEmpty={this.handleChange}
          isFrom={'source_page'}
          session={this.props.session}
        />
      );
    } else if (isTechComparisonChart(value)) {
      return (
        <TechnicianMonthComparison
          removeFav={this.removeFav}
          datatype={''}
          handleClosePopup={this.handleClose}
          callFrom={0}
          handleChartPopup={this.handleChartPopup}
          month1={this.props.comparisonMonths[0]}
          handleClose={this.handleClose}
          month2={this.props.comparisonMonths[1]}
          chartId={Number(value)}
          source={'source_page'}
          session={this.props.session}
        />
      );
    } else if (isAdvisorComparisonChart(value)) {
      return (
        <AdvisorComparisonCharts
          removeFav={this.removeFav}
          datatype={''}
          handleChartPopup={this.handleChartPopup}
          handleClosePopup={this.handleClose}
          handleClose={this.handleClose}
          month1={this.props.comparisonMonths[0]}
          month2={this.props.comparisonMonths[1]}
          chartId={Number(value)}
          isFrom={'source_page'}
        />
      );
    } else if (isAdvisorOpcategoryChart(value)) {
      return (
        <AdvisorOpcategoryCharts
          removeFav={this.removeFav}
          datatype={''}
          handleChartPopup={this.handleChartPopup}
          handleClosePopup={this.handleClose}
          handleClose={this.handleClose}
          month1={this.props.comparisonMonths[0]}
          chartId={Number(value)}
          isFrom={'source_page'}
        />
      );
    } else if (isMonthComparisonChart(value) || isPartsComparisonChart(value)) {
      return (
        <HighCharts
          removeFav={this.removeFav}
          isPartsCharts={isPartsComparisonChart(value) ? true : false}
          datatype={''}
          handleChartPopup={this.handleChartPopup}
          handleClose={this.handleClose}
          handleClosePopup={this.handleClose}
          chartId={Number(value)}
          month1={this.props.comparisonMonths[0]}
          month2={this.props.comparisonMonths[1]}
          isFrom={'source_page'}
        />
      );
    } else if (isDiscountComparisonApexChart(value)) {
      return (
        <ApexChartsDiscount
          removeFav={this.removeFav}
          datatype={''}
          chartId={Number(value)}
          handleClosePopup={this.handleClose}
          handleChartPopup={this.handleChartPopup}
          handleClose={this.handleClose}
          month1={this.props.comparisonMonths[0]}
          month2={this.props.comparisonMonths[1]}
          isFrom={'source_page'}
        />
      );
    } else if (isAddonsComparisonChart(value)) {
      return (
        <ApexCharts
          removeFav={this.removeFav}
          datatype={''}
          handleChartPopup={this.handleChartPopup}
          handleClosePopup={this.handleClose}
          handleClose={this.handleClose}
          chartId={Number(value)}
          month1={this.props.comparisonMonths[0]}
          month2={this.props.comparisonMonths[1]}
          isFrom={'source_page'}
        />
      );
    } else if (isOpportunityChart(value)) {
      return (
        <OpportunityCharts
          removeFav={this.removeFav}
          handleChartPopup={this.handleChartPopup}
          handleClosePopup={this.handleClose}
          handleClose={this.handleClose}
          chartId={Number(value)}
        />
      );
    } else {
      return (
        <DashboardBarRenderer
          removeFav={this.removeFav}
          handleChartPopup={this.handleChartPopup}
          handleClosePopup={this.handleClose}
          handleClose={this.handleClose}
          chartId={Number(value)}
          isFrom={'source_page'}
        />
      );
    }
  };

  render() {
    setTimeout(() => {
      this.setState({ timePassed: true });
    }, 1000);
    
    const favourites = this.props.favourites.filter(Number);
    const favouritesArr = favourites.filter(n => n);
    const { classes } = this.props;
    let oneRowCharts = [];
    let twoRowCharts = [];

    if (favouritesArr.length > 0) {
      favouritesArr.map((value, index) => {
        if (
          isTechComparisonChart(value) ||
          isMonthComparisonChart(value) ||
          isPartsComparisonChart(value) ||
          isDiscountComparisonApexChart(value) ||
          isAddonsComparisonChart(value) ||
          isOpportunityChart(value) ||
          (isTechChart(value) && value == 1352) ||
          (isTechChart(value) && value == 1363) ||
          (isTechChart(value) && value == 1358) ||
          (isTechChart(value) && value == 1345)
        ) {
          oneRowCharts.push(value);
        } else {
          twoRowCharts.push(value);
        }
      });
    }

    return (
      <div className={classes.root}>
        <Paper className={classes.paper}>
          <PageHeader
            title={'My Favorites'}
            setResetDashboard={this.setResetDashboard}
            removeFav={this.removeFav}
            setFilterCharts={this.setFilterCharts}
            isLoading={this.state.isLoading}
          />
          <Divider />
          
          {(this.props.favouriteData == 0 ||
            this.props.favouriteData.length == undefined) &&
          this.props.favourites &&
          (this.props.favourites == 0 || this.props.favourites.length == 0) ? (
            <div>
              <div style={{ textAlign: 'center', marginTop: '20px' }}>
                <StarBorderIcon style={{ fontSize: 60, color: '#ccc' }} />
                <h3>No favorites added yet</h3>
                <p>Add charts to your favorites to see them here</p>
              </div>
            </div>
          ) : (
            // MUI Grid Layout
            <Box sx={{ flexGrow: 1, padding: 2 }}>
              <Grid container spacing={2}>
                {/* First render half-width charts (2 per row) */}
                {twoRowCharts.map((value, index) => {
                  console.log('value---------twoRowCharts', JSON.stringify(twoRowCharts));
                  return (
                    <Grid item xs={12} sm={6} key={`two-row-${index}`}>
                      <Paper 
                        elevation={2} 
                        sx={{ 
                          height: '315px', // Equivalent to h: 7 * 45px
                          padding: 1,
                          display: 'flex',
                          flexDirection: 'column'
                        }}
                        className="diagram-section"
                        id={`chart-${value}`}
                      >
                        {this.renderChart(value)}
                      </Paper>
                    </Grid>
                  );
                })}
                
                {/* Then render full-width charts (1 per row) */}
                {oneRowCharts.map((value, index) => (
                  <Grid item xs={12} key={`one-row-${index}`}>
                    <Paper 
                      elevation={2} 
                      sx={{ 
                        height: '360px', // Equivalent to h: 8 * 45px
                        padding: 1,
                        display: 'flex',
                        flexDirection: 'column'
                      }}
                      className={clsx(classes.gridContainer, 'diagram-section one')}
                      id={`chart-${value}`}
                    >
                      {this.renderChart(value)}
                    </Paper>
                  </Grid>
                ))}
              </Grid>
            </Box>
          )}
        </Paper>
      </div>
    );
  }
}

const styles = theme => ({
  closeButton: {
    position: 'absolute',
    right: theme.spacing(1),
    top: theme.spacing(1),
    color: theme.palette.grey[500]
  },
  root: {
    flexGrow: 1,
    backgroundColor: theme.palette.background.paper,
    display: 'flex',
    height: '100%'
  },
  paper: {
    padding: theme.spacing(2),
    margin: 'auto',
    maxWidth: '100%',
    flexGrow: 1
  },
  gridContainer: {
    padding: 5
  }
});

export default withStyles(styles)(ChartsMUI);
