import React from 'react';
import { Paper, Grid, Box, Typography, IconButton } from '@material-ui/core';
import { withStyles } from '@material-ui/styles';
import MoreVertIcon from '@material-ui/icons/MoreVert';
import InfoIcon from '@material-ui/icons/Info';
import GetAppIcon from '@material-ui/icons/GetApp';
import StarIcon from '@material-ui/icons/Star';

// Demo component to show the exact same layout as the image
class ChartsMUIDemo extends React.Component {
  render() {
    const { classes } = this.props;

    // Mock data representing the charts from the image
    const twoRowCharts = [
      { id: 1247, title: 'ELR', type: 'bar' },
      { id: 1287, title: 'Total Revenue', type: 'horizontal-bar' },
      { id: 1299, title: 'Total Revenue', type: 'horizontal-bar' },
      { id: 939, title: 'CP Gross Profit', type: 'line' }
    ];

    const oneRowCharts = [
      // These would be full-width charts if any existed in the demo
    ];

    return (
      <div className={classes.root}>
        <Paper className={classes.paper}>
          <Typography variant="h5" className={classes.title}>
            My Favorites - MUI Layout Demo
          </Typography>
          
          {/* MUI Grid Layout - Exact same as ReactGridLayout */}
          <Box className={classes.container}>
            <Grid container spacing={2}>
              {/* First render half-width charts (2 per row) */}
              {twoRowCharts.map((chart, index) => (
                <Grid item xs={12} sm={6} key={`two-row-${index}`}>
                  <Paper 
                    elevation={2} 
                    className={classes.chartPaper}
                  >
                    {/* Chart Header */}
                    <div className={classes.chartHeader}>
                      <Typography variant="h6" className={classes.chartTitle}>
                        {chart.title} <span className={classes.chartId}>{chart.id}</span>
                      </Typography>
                      <div className={classes.chartActions}>
                        <IconButton size="small" className={classes.actionButton}>
                          <MoreVertIcon />
                        </IconButton>
                        <IconButton size="small" className={classes.actionButton}>
                          <InfoIcon />
                        </IconButton>
                        <IconButton size="small" className={classes.actionButton}>
                          <GetAppIcon />
                        </IconButton>
                        <IconButton size="small" className={classes.actionButton}>
                          <StarIcon />
                        </IconButton>
                      </div>
                    </div>

                    {/* Chart Content Area */}
                    <div className={classes.chartContent}>
                      {chart.type === 'bar' && (
                        <div className={classes.mockBarChart}>
                          <div className={classes.barChartContainer}>
                            {/* Mock bar chart visualization */}
                            <div className={classes.barGroup}>
                              <div className={classes.bar} style={{ height: '60%', backgroundColor: '#8884d8' }}></div>
                              <div className={classes.bar} style={{ height: '40%', backgroundColor: '#82ca9d' }}></div>
                              <div className={classes.bar} style={{ height: '30%', backgroundColor: '#ffc658' }}></div>
                            </div>
                            <div className={classes.barGroup}>
                              <div className={classes.bar} style={{ height: '70%', backgroundColor: '#8884d8' }}></div>
                              <div className={classes.bar} style={{ height: '50%', backgroundColor: '#82ca9d' }}></div>
                              <div className={classes.bar} style={{ height: '35%', backgroundColor: '#ffc658' }}></div>
                            </div>
                            <div className={classes.barGroup}>
                              <div className={classes.bar} style={{ height: '55%', backgroundColor: '#8884d8' }}></div>
                              <div className={classes.bar} style={{ height: '45%', backgroundColor: '#82ca9d' }}></div>
                              <div className={classes.bar} style={{ height: '25%', backgroundColor: '#ffc658' }}></div>
                            </div>
                            <div className={classes.barGroup}>
                              <div className={classes.bar} style={{ height: '80%', backgroundColor: '#8884d8' }}></div>
                              <div className={classes.bar} style={{ height: '60%', backgroundColor: '#82ca9d' }}></div>
                              <div className={classes.bar} style={{ height: '40%', backgroundColor: '#ffc658' }}></div>
                            </div>
                          </div>
                          <div className={classes.legend}>
                            <span className={classes.legendItem}>
                              <span className={classes.legendColor} style={{ backgroundColor: '#8884d8' }}></span>
                              Competitive
                            </span>
                            <span className={classes.legendItem}>
                              <span className={classes.legendColor} style={{ backgroundColor: '#82ca9d' }}></span>
                              Maintenance
                            </span>
                            <span className={classes.legendItem}>
                              <span className={classes.legendColor} style={{ backgroundColor: '#ffc658' }}></span>
                              Repair
                            </span>
                          </div>
                        </div>
                      )}

                      {chart.type === 'horizontal-bar' && (
                        <div className={classes.mockHorizontalBarChart}>
                          <div className={classes.horizontalBarContainer}>
                            {/* Mock horizontal bar chart */}
                            <div className={classes.horizontalBarRow}>
                              <span className={classes.barLabel}>Shands Bellview [50036]</span>
                              <div className={classes.horizontalBar} style={{ width: '90%', backgroundColor: '#4285f4' }}></div>
                            </div>
                            <div className={classes.horizontalBarRow}>
                              <span className={classes.barLabel}>Fontana Renewal [50036]</span>
                              <div className={classes.horizontalBar} style={{ width: '75%', backgroundColor: '#4285f4' }}></div>
                            </div>
                            <div className={classes.horizontalBarRow}>
                              <span className={classes.barLabel}>Matthews Suzuki [50036]</span>
                              <div className={classes.horizontalBar} style={{ width: '85%', backgroundColor: '#ea4335' }}></div>
                            </div>
                            <div className={classes.horizontalBarRow}>
                              <span className={classes.barLabel}>Emery Sienna [50036]</span>
                              <div className={classes.horizontalBar} style={{ width: '45%', backgroundColor: '#4285f4' }}></div>
                            </div>
                            <div className={classes.horizontalBarRow}>
                              <span className={classes.barLabel}>Shawn Elza [50036]</span>
                              <div className={classes.horizontalBar} style={{ width: '40%', backgroundColor: '#4285f4' }}></div>
                            </div>
                            <div className={classes.horizontalBarRow}>
                              <span className={classes.barLabel}>Billy Smith [50036]</span>
                              <div className={classes.horizontalBar} style={{ width: '95%', backgroundColor: '#ea4335' }}></div>
                            </div>
                          </div>
                        </div>
                      )}

                      {chart.type === 'line' && (
                        <div className={classes.mockLineChart}>
                          <div className={classes.lineChartContainer}>
                            {/* Mock line chart visualization */}
                            <svg width="100%" height="200" className={classes.lineSvg}>
                              <polyline
                                fill="none"
                                stroke="#4285f4"
                                strokeWidth="2"
                                points="20,150 60,120 100,130 140,110 180,100 220,90 260,95 300,85 340,80 380,75"
                              />
                              <polyline
                                fill="none"
                                stroke="#34a853"
                                strokeWidth="2"
                                points="20,170 60,160 100,165 140,155 180,150 220,145 260,150 300,140 340,135 380,130"
                              />
                              <polyline
                                fill="none"
                                stroke="#ea4335"
                                strokeWidth="2"
                                points="20,180 60,175 100,180 140,170 180,165 220,160 260,165 300,155 340,150 380,145"
                              />
                            </svg>
                          </div>
                          <div className={classes.legend}>
                            <span className={classes.legendItem}>
                              <span className={classes.legendColor} style={{ backgroundColor: '#4285f4' }}></span>
                              Labor Gross Profit
                            </span>
                            <span className={classes.legendItem}>
                              <span className={classes.legendColor} style={{ backgroundColor: '#34a853' }}></span>
                              Parts Gross Profit
                            </span>
                            <span className={classes.legendItem}>
                              <span className={classes.legendColor} style={{ backgroundColor: '#ea4335' }}></span>
                              Combined Gross Profit
                            </span>
                          </div>
                        </div>
                      )}
                    </div>
                  </Paper>
                </Grid>
              ))}
              
              {/* Then render full-width charts (1 per row) */}
              {oneRowCharts.map((chart, index) => (
                <Grid item xs={12} key={`one-row-${index}`}>
                  <Paper 
                    elevation={2} 
                    className={classes.fullWidthChartPaper}
                  >
                    <div className={classes.chartHeader}>
                      <Typography variant="h6" className={classes.chartTitle}>
                        {chart.title} <span className={classes.chartId}>{chart.id}</span>
                      </Typography>
                      <div className={classes.chartActions}>
                        <IconButton size="small" className={classes.actionButton}>
                          <MoreVertIcon />
                        </IconButton>
                        <IconButton size="small" className={classes.actionButton}>
                          <InfoIcon />
                        </IconButton>
                        <IconButton size="small" className={classes.actionButton}>
                          <GetAppIcon />
                        </IconButton>
                        <IconButton size="small" className={classes.actionButton}>
                          <StarIcon />
                        </IconButton>
                      </div>
                    </div>
                    <div className={classes.chartContent}>
                      {/* Full width chart content would go here */}
                      <div style={{ height: '300px', display: 'flex', alignItems: 'center', justifyContent: 'center', backgroundColor: '#f5f5f5' }}>
                        <Typography variant="body1" color="textSecondary">
                          Full Width Chart: {chart.title}
                        </Typography>
                      </div>
                    </div>
                  </Paper>
                </Grid>
              ))}
            </Grid>
          </Box>
        </Paper>
      </div>
    );
  }
}

const styles = theme => ({
  root: {
    flexGrow: 1,
    backgroundColor: theme.palette.background.default,
    padding: theme.spacing(2)
  },
  paper: {
    padding: theme.spacing(2),
    maxWidth: '100%'
  },
  title: {
    marginBottom: theme.spacing(2),
    fontWeight: 'bold'
  },
  container: {
    flexGrow: 1,
    padding: theme.spacing(1)
  },
  chartPaper: {
    height: '315px', // Equivalent to ReactGridLayout h: 7 * 45px
    display: 'flex',
    flexDirection: 'column',
    border: '1px solid #e0e0e0'
  },
  fullWidthChartPaper: {
    height: '360px', // Equivalent to ReactGridLayout h: 8 * 45px
    display: 'flex',
    flexDirection: 'column',
    border: '1px solid #e0e0e0'
  },
  chartHeader: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: theme.spacing(1),
    borderBottom: '1px solid #e0e0e0',
    minHeight: '48px'
  },
  chartTitle: {
    fontSize: '14px',
    fontWeight: 'bold',
    color: '#333'
  },
  chartId: {
    fontSize: '12px',
    color: '#666',
    fontWeight: 'normal'
  },
  chartActions: {
    display: 'flex',
    gap: theme.spacing(0.5)
  },
  actionButton: {
    padding: '4px',
    '& svg': {
      fontSize: '16px'
    }
  },
  chartContent: {
    flex: 1,
    padding: theme.spacing(1),
    overflow: 'hidden'
  },
  mockBarChart: {
    height: '100%',
    display: 'flex',
    flexDirection: 'column'
  },
  barChartContainer: {
    flex: 1,
    display: 'flex',
    alignItems: 'flex-end',
    justifyContent: 'space-around',
    padding: theme.spacing(1)
  },
  barGroup: {
    display: 'flex',
    alignItems: 'flex-end',
    gap: '2px',
    height: '100%'
  },
  bar: {
    width: '8px',
    minHeight: '10px'
  },
  legend: {
    display: 'flex',
    justifyContent: 'center',
    gap: theme.spacing(2),
    padding: theme.spacing(1),
    fontSize: '12px'
  },
  legendItem: {
    display: 'flex',
    alignItems: 'center',
    gap: '4px'
  },
  legendColor: {
    width: '12px',
    height: '12px',
    borderRadius: '2px'
  },
  mockHorizontalBarChart: {
    height: '100%',
    padding: theme.spacing(1)
  },
  horizontalBarContainer: {
    display: 'flex',
    flexDirection: 'column',
    gap: theme.spacing(1)
  },
  horizontalBarRow: {
    display: 'flex',
    alignItems: 'center',
    gap: theme.spacing(1)
  },
  barLabel: {
    fontSize: '11px',
    minWidth: '120px',
    textAlign: 'right'
  },
  horizontalBar: {
    height: '16px',
    minWidth: '20px'
  },
  mockLineChart: {
    height: '100%',
    display: 'flex',
    flexDirection: 'column'
  },
  lineChartContainer: {
    flex: 1
  },
  lineSvg: {
    border: '1px solid #f0f0f0'
  }
});

export default withStyles(styles)(ChartsMUIDemo);
