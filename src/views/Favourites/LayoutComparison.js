import React, { useState } from 'react';
import { Paper, Button, Box, Typography, Switch, FormControlLabel } from '@material-ui/core';
import { withStyles } from '@material-ui/styles';
import ChartsMUIDemo from './ChartsMUIDemo';

// Component to compare ReactGridLayout vs MUI Grid Layout
class LayoutComparison extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      showMUILayout: true
    };
  }

  toggleLayout = () => {
    this.setState(prevState => ({
      showMUILayout: !prevState.showMUILayout
    }));
  };

  render() {
    const { classes } = this.props;
    const { showMUILayout } = this.state;

    return (
      <div className={classes.root}>
        <Paper className={classes.controlPanel}>
          <Typography variant="h4" className={classes.title}>
            Layout Comparison: ReactGridLayout vs MUI Grid
          </Typography>
          
          <Box className={classes.controls}>
            <FormControlLabel
              control={
                <Switch
                  checked={showMUILayout}
                  onChange={this.toggleLayout}
                  color="primary"
                />
              }
              label={showMUILayout ? "MUI Grid Layout (Recommended)" : "ReactGridLayout (Original)"}
            />
          </Box>

          <Box className={classes.description}>
            <Typography variant="body1">
              {showMUILayout ? (
                <>
                  <strong>MUI Grid Layout Benefits:</strong>
                  <ul>
                    <li>✅ No external dependencies (react-grid-layout, react-resizable)</li>
                    <li>✅ Better responsive behavior</li>
                    <li>✅ Consistent spacing and alignment</li>
                    <li>✅ Faster rendering performance</li>
                    <li>✅ Native Material-UI integration</li>
                    <li>✅ Easier maintenance and debugging</li>
                    <li>✅ Better mobile experience</li>
                  </ul>
                </>
              ) : (
                <>
                  <strong>ReactGridLayout Issues:</strong>
                  <ul>
                    <li>❌ Requires external dependencies</li>
                    <li>❌ Alignment issues on different screen sizes</li>
                    <li>❌ Complex CSS requirements</li>
                    <li>❌ Performance overhead</li>
                    <li>❌ Limited responsive capabilities</li>
                    <li>❌ Maintenance complexity</li>
                  </ul>
                </>
              )}
            </Typography>
          </Box>
        </Paper>

        <Box className={classes.layoutContainer}>
          {showMUILayout ? (
            <ChartsMUIDemo />
          ) : (
            <Paper className={classes.placeholderPaper}>
              <Typography variant="h6" align="center" color="textSecondary">
                ReactGridLayout Demo
              </Typography>
              <Typography variant="body2" align="center" color="textSecondary" style={{ marginTop: 16 }}>
                The original ReactGridLayout implementation would be shown here.
                <br />
                Switch to MUI Layout to see the improved version.
              </Typography>
              
              <Box className={classes.mockGridLayout}>
                <div className={classes.mockGridItem}>
                  <Typography variant="caption">Chart 1 (Half Width)</Typography>
                </div>
                <div className={classes.mockGridItem}>
                  <Typography variant="caption">Chart 2 (Half Width)</Typography>
                </div>
                <div className={classes.mockGridItem}>
                  <Typography variant="caption">Chart 3 (Half Width)</Typography>
                </div>
                <div className={classes.mockGridItem}>
                  <Typography variant="caption">Chart 4 (Half Width)</Typography>
                </div>
                <div className={classes.mockGridItemFull}>
                  <Typography variant="caption">Chart 5 (Full Width)</Typography>
                </div>
              </Box>
            </Paper>
          )}
        </Box>

        <Paper className={classes.implementationDetails}>
          <Typography variant="h6" className={classes.sectionTitle}>
            Implementation Details
          </Typography>
          
          <Box className={classes.codeComparison}>
            <div className={classes.codeBlock}>
              <Typography variant="subtitle2" className={classes.codeTitle}>
                ReactGridLayout (Original)
              </Typography>
              <pre className={classes.code}>
{`<ReactGridLayout
  className="layout"
  cols={12}
  rowHeight={45}
  layout={this.state.layout}
  onLayoutChange={this.onLayoutChange}
  isResizable={false}
  isDraggable={false}
>
  {charts.map((value, index) => (
    <div
      key={index}
      data-grid={getDataGridConfiguration(index)}
    >
      {/* Chart content */}
    </div>
  ))}
</ReactGridLayout>`}
              </pre>
            </div>

            <div className={classes.codeBlock}>
              <Typography variant="subtitle2" className={classes.codeTitle}>
                MUI Grid (New)
              </Typography>
              <pre className={classes.code}>
{`<Box sx={{ flexGrow: 1, padding: 2 }}>
  <Grid container spacing={2}>
    {/* Half-width charts */}
    {twoRowCharts.map((value, index) => (
      <Grid item xs={12} sm={6} key={index}>
        <Paper elevation={2} sx={{ height: '315px' }}>
          {/* Chart content */}
        </Paper>
      </Grid>
    ))}
    
    {/* Full-width charts */}
    {oneRowCharts.map((value, index) => (
      <Grid item xs={12} key={index}>
        <Paper elevation={2} sx={{ height: '360px' }}>
          {/* Chart content */}
        </Paper>
      </Grid>
    ))}
  </Grid>
</Box>`}
              </pre>
            </div>
          </Box>
        </Paper>
      </div>
    );
  }
}

const styles = theme => ({
  root: {
    padding: theme.spacing(2),
    backgroundColor: theme.palette.background.default,
    minHeight: '100vh'
  },
  controlPanel: {
    padding: theme.spacing(3),
    marginBottom: theme.spacing(2)
  },
  title: {
    marginBottom: theme.spacing(2),
    color: theme.palette.primary.main
  },
  controls: {
    marginBottom: theme.spacing(2)
  },
  description: {
    '& ul': {
      paddingLeft: theme.spacing(2)
    },
    '& li': {
      marginBottom: theme.spacing(0.5)
    }
  },
  layoutContainer: {
    marginBottom: theme.spacing(2)
  },
  placeholderPaper: {
    padding: theme.spacing(4),
    textAlign: 'center'
  },
  mockGridLayout: {
    display: 'grid',
    gridTemplateColumns: 'repeat(2, 1fr)',
    gap: theme.spacing(2),
    marginTop: theme.spacing(3),
    maxWidth: 600,
    margin: '0 auto'
  },
  mockGridItem: {
    height: 150,
    backgroundColor: theme.palette.grey[200],
    border: `2px dashed ${theme.palette.grey[400]}`,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: theme.shape.borderRadius
  },
  mockGridItemFull: {
    gridColumn: '1 / -1',
    height: 180,
    backgroundColor: theme.palette.grey[200],
    border: `2px dashed ${theme.palette.grey[400]}`,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: theme.shape.borderRadius
  },
  implementationDetails: {
    padding: theme.spacing(3)
  },
  sectionTitle: {
    marginBottom: theme.spacing(2),
    color: theme.palette.primary.main
  },
  codeComparison: {
    display: 'grid',
    gridTemplateColumns: 'repeat(auto-fit, minmax(400px, 1fr))',
    gap: theme.spacing(2)
  },
  codeBlock: {
    backgroundColor: theme.palette.grey[50],
    border: `1px solid ${theme.palette.grey[300]}`,
    borderRadius: theme.shape.borderRadius,
    overflow: 'hidden'
  },
  codeTitle: {
    padding: theme.spacing(1, 2),
    backgroundColor: theme.palette.grey[200],
    borderBottom: `1px solid ${theme.palette.grey[300]}`,
    fontWeight: 'bold'
  },
  code: {
    padding: theme.spacing(2),
    margin: 0,
    fontSize: '12px',
    lineHeight: 1.4,
    overflow: 'auto',
    backgroundColor: 'transparent',
    fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace'
  }
});

export default withStyles(styles)(LayoutComparison);
