import { CircularProgress, Grid, Paper } from '@material-ui/core';
import moment from 'moment';
import React, { useState, useEffect } from 'react';
import ReactApexChart from 'react-apexcharts';
import {
  Card,
  CardContent,
  CardHeader,
  Divider,
  Typography
} from '@material-ui/core';
import {
  getChartName,
  getSubHeader,
  getChartDataIndex,
  getChartDrillDown
} from 'src/components/ViewGraphDetailsAction';
import MoreActions from 'src/components/MoreActions';
import { useHistory } from 'react-router';
import { useDispatch, useSelector } from 'react-redux';

import ChartDialog from 'src/components/Dialog';
import PropTypes, { element } from 'prop-types';
import {
  gettechMetricsTechJob,
  gettechMetrics1352,
  gettechMetricsSoldhoursWeekly,
  gettechMetricsSingleJobROCount,
  getTechProductivityAllRos
} from 'src/utils/hasuraServices';
import { makeStyles } from '@material-ui/core/styles';
import { chart } from 'highcharts';
var lodash = require('lodash');
const useStyles = makeStyles({
  loaderGrid: {
    height: 300,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center'
  }
});
const BarChartRendererPostgraphile = ({
  chartId,
  removeFav,
  title,
  tech,
  resultSet,
  techName,
  checkEmpty,
  parentCallback,
  handleClosePopup,
  realm,
  type,
  chartTitle,
  callFrom,
  isFrom,
  tabSelection,
  mainTabSelection,
  selectedToggle,
  ChartData
}) => {
  const classes = useStyles();
  const [open, setOpen] = useState(false);
  const session = useSelector(state => state.session);
  //let title = getChartName(chartId);

  const history = useHistory();
  const [queryState, queryChangedState] = useState({});
  const [data, setData] = useState([]);
  const [selectedTech, setSelectedTech] = useState([]);
  const [isLoading, setLoading] = useState(true);
  const [isTechLoading, setTechLoading] = useState(true);
  const [chartoptions, setchartOptions] = useState({});
  const [series, setSeries] = useState([]);
  const [years, setYears] = useState('');
  const [isEmpty, setIsEmpty] = useState(false);
  const dispatch = useDispatch();
  useEffect(() => {
    setLoading(true);
    setSeries([]);
    setTechLoading(true);
    setSeries([]);
    getDataforTechCharts(chartId);
  }, [tech, session.storeSelected]);

  var chartList = JSON.parse(global.localStorage.getItem('chart-master'));
  let filteredResult = chartList.filter(item => item.chartId == chartId);
  let chartLoc;
  if (filteredResult[0].dbdName == 'Parts Workmix') {
    chartLoc = 'PartsWorkMixAnalysis';
  } else {
    chartLoc = 'LaborWorkMixAnalysis';
  }
  const removeFavourite = val => {
    removeFav(val);
  };
  const chartPopup = val => {
    setOpen(true);
  };
  const handleClose = () => {
    setOpen(false);
    handleClosePopup(1);
  };
  let isEmptyCheck = false;
  const getDataforTechCharts = chartId => {
    tech = tech == '' ? 'All' : tech;
    if (chartId == 1352) {
      gettechMetrics1352(tech, callback => {
        if (callback) {
          let dataArr = JSON.parse(
            callback.data
              .statelessDbdPeopleMetricsTechnicianGetChartsEstimatedTechnicianSoldhoursWeeklyall
              .chartsEstimatedTechnicianSoldhoursWeeklyalls[0].jsonData
          );
          setData(dataArr);

          let data = dataArr[0].datasets;
          const labels = dataArr[0].labels;
          const lastyears = labels.map(dateString => {
            const date = new Date(dateString);
            return date.getFullYear();
          });
          isEmptyCheck = lodash.every(dataArr[0].datasets, item => {
            return lodash.every(item.data, value => parseFloat(value) === 0);
          });

          setYears(labels);
          const result = data;
          setLoading(false);
          const updatedData = result.map(item => {
            return {
              name: item.label,
              data: item.data,
              //  data: item.data.filter(value => value !== '0'),
              month: JSON.parse(
                callback.data
                  .statelessDbdPeopleMetricsTechnicianGetChartsEstimatedTechnicianSoldhoursWeeklyall
                  .chartsEstimatedTechnicianSoldhoursWeeklyalls[0].jsonData
              )[0].labels.map(item => moment(item).format('MMM DD')),
              monthYear: JSON.parse(
                callback.data
                  .statelessDbdPeopleMetricsTechnicianGetChartsEstimatedTechnicianSoldhoursWeeklyall
                  .chartsEstimatedTechnicianSoldhoursWeeklyalls[0].jsonData
              )[0].labels.map(item => moment(item).format('YYYY-MM-DD'))
            };
          });

          const seriesData = updatedData;
          setSeries(seriesData);
          if (isEmptyCheck) {
            setIsEmpty(true);
          } else {
            setIsEmpty(false);
          }
        }
      });
    }
    if (chartId == 1345) {
      gettechMetricsSoldhoursWeekly(tech, callback => {
        if (
          callback.data
            .statelessDbdPeopleMetricsTechnicianGetChartsEstimatedTechProductivityWeekly
            .chartsEstimatedTechProductivityWeeklies
        ) {
          setLoading(false);
          let dataArr = JSON.parse(
            callback.data
              .statelessDbdPeopleMetricsTechnicianGetChartsEstimatedTechProductivityWeekly
              .chartsEstimatedTechProductivityWeeklies[0].jsonData
          );
          setData(dataArr);

          let data = dataArr[0].datasets;

          const result = data;

          setLoading(false);
          const updatedData = result.map(item => {
            return {
              name: item.label,
              data: item.data.filter(value => value !== '0'),
              month: JSON.parse(
                callback.data
                  .statelessDbdPeopleMetricsTechnicianGetChartsEstimatedTechProductivityWeekly
                  .chartsEstimatedTechProductivityWeeklies[0].jsonData
              )[0].labels.map(item => moment(item).format('MMM DD'))
            };
          });

          const seriesData = updatedData;
          setSeries(seriesData);
        }
      });
    }
    if (chartId == 1347 || chartId == 1348) {
      if (
        ChartData?.data?.statelessDbdPeopleMetricsTechnicianGetTechjob?.techjobs
      ) {
        const jsonData =
          ChartData.data.statelessDbdPeopleMetricsTechnicianGetTechjob
            .techjobs[0].jsonData;
        processTechJobData(jsonData, chartId);
      } else {
        gettechMetricsTechJob(tech, callback => {
          const techjobs =
            callback.data.statelessDbdPeopleMetricsTechnicianGetTechjob
              .techjobs;
          if (techjobs) {
            const jsonData = techjobs[0].jsonData;
            processTechJobData(jsonData, chartId);
          }
        });
      }
    }
    if (chartId == 1358) {
      gettechMetricsSingleJobROCount(tech, callback => {
        if (
          callback.data
            .statelessDbdPeopleMetricsTechnicianGetTechMetricsSingleJobRoCount
            .statelessCcPhysicalRwGetAllSchemaFunctionReturns
        ) {
          let dataArr = JSON.parse(
            callback.data
              .statelessDbdPeopleMetricsTechnicianGetTechMetricsSingleJobRoCount
              .statelessCcPhysicalRwGetAllSchemaFunctionReturns[0].jsonData
          );

          let data = dataArr[0].datasets;
          isEmptyCheck = lodash.every(dataArr[0].datasets, item => {
            return lodash.every(item.data, value => parseFloat(value) === 0);
          });
          setData(dataArr);
          const result = data;
          setLoading(false);

          const updatedData = result.map(item => {
            return {
              name: item.label,
              data: item.data,
              month: dataArr[0].labels.map(item =>
                moment(item).format('MMM YY')
              )
            };
          });

          const seriesData = updatedData;
          setSeries(seriesData);
          if (isEmptyCheck) {
            setIsEmpty(true);
          } else {
            setIsEmpty(false);
          }
        }
      });
    }
    if (chartId == 1363) {
      getTechProductivityAllRos(tech, callback => {
        if (callback) {
          let dataArr = JSON.parse(
            callback.data
              .statelessDbdPeopleMetricsTechnicianGetChartsEstimatedTechnicianRocountWeeklyall
              .statelessCcPhysicalRwGetAllSchemaFunctionReturns[0].jsonData
          );
          setData(dataArr);

          let data = dataArr[0].datasets;
          const labels = dataArr[0].labels;
          const lastyears = labels.map(dateString => {
            const date = new Date(dateString);
            return date.getFullYear();
          });
          isEmptyCheck = lodash.every(dataArr[0].datasets, item => {
            return lodash.every(item.data, value => parseFloat(value) === 0);
          });

          setYears(labels);
          const result = data;
          setLoading(false);
          const updatedData = result.map(item => {
            return {
              name: item.label,
              data: item.data,
              //  data: item.data.filter(value => value !== '0'),
              month: JSON.parse(
                callback.data
                  .statelessDbdPeopleMetricsTechnicianGetChartsEstimatedTechnicianRocountWeeklyall
                  .statelessCcPhysicalRwGetAllSchemaFunctionReturns[0].jsonData
              )[0].labels.map(item => moment(item).format('MMM DD'))
            };
          });

          const seriesData = updatedData;
          setSeries(seriesData);
          if (isEmptyCheck) {
            setIsEmpty(true);
          } else {
            setIsEmpty(false);
          }
        }
      });
    }
  };

  const processTechJobData = (jsonData, chartId) => {
    const parsedData = JSON.parse(jsonData);
    const filteredData = parsedData.map(item => {
      item.datasets = item.datasets.filter(
        dataset => dataset.chartId == chartId.toString()
      );
      return item;
    });

    const datasets = filteredData[0]?.datasets[0];
    if (!datasets) return;

    const updatedData = {
      label: datasets.label,
      chartId: datasets.chartId,
      data: datasets.data,
      month: parsedData[0].labels.map(item => moment(item).format('MMM YY'))
    };

    const data = updatedData.data.map(value => parseFloat(value));
    const seriesData = [
      { name: updatedData.label, data, month: updatedData.month }
    ];

    setData(parsedData);
    setSeries(seriesData);
    setLoading(false);
  };
  useEffect(() => {
    setSelectedTech(tech);
    processDataTech(tech);

    let opt = chartoptions;
    if (series.length > 0 && !isLoading) {
      opt.xaxis.categories =
        chartId == 1347 || chartId == 1348 ? series[0].month : series[0].month;
      setchartOptions(opt);
      setTechLoading(false);
    } else if (
      !isLoading &&
      series.length == 0 &&
      ((data && data[0].datasets && data[0].datasets[0].data == null) ||
        data == null)
    ) {
      setTechLoading(false);
    }
    // else if (
    //   !isLoading &&
    //   ((data && data[0].datasets && data[0].datasets[0].data == null) ||
    //     data == null)
    // ) {
    //   setTechLoading(false);
    // }
  }, [series, isLoading]);
  const processDataTech = techi => {
    var payTypeSaved = localStorage.getItem('retailFlags').split(',');
    const options = {
      // colors: ['#03aafc', '#ee4848', '#3bc15e'],
      // colors: ['#0389fc', '#e14f4f', '#35af55'],

      chart: {
        id: 'multi-line-chart',
        type: 'line',

        zoom: {
          enabled: false
        },
        stacked: false,
        fontFamily: 'Roboto,Helvetica, Arial, sans-serif',
        toolbar: {
          show: false,
          autoSelected: 'zoom'
        },
        events: {
          mounted: function(chartContext) {
            if (chartContext.w.globals.dom.baseEl) {
              chartContext.w.globals.dom.baseEl.style.cursor = 'pointer';
            }
          },
          click: function(event, chartContext, config) {
            let techId = localStorage.getItem('selectedTechName');
            let technician = localStorage.getItem('selectedTech');

            if (
              config &&
              config.config &&
              config.config.series &&
              Array.isArray(config.config.series) &&
              config.seriesIndex >= 0 &&
              config.seriesIndex < config.config.series.length &&
              config.config.series[config.seriesIndex].data &&
              Array.isArray(config.config.series[config.seriesIndex].data) &&
              config.dataPointIndex >= 0 &&
              config.dataPointIndex <
                config.config.series[config.seriesIndex].data.length
            ) {
              let formattedMonth = '';
              let weekEnd = '';
              let weekStart = '';
              let monthYear =
                chartId == 1352 || chartId == 1345 || chartId == 1363
                  ? config.globals.categoryLabels[config.dataPointIndex].split(
                      ' '
                    )
                  : config.globals.labels[config.dataPointIndex].split(' ');

              let dataIndex = config.seriesIndex;
              let month = moment()
                .month(monthYear[0])
                .format('MM');

              let date = moment(monthYear[1], 'YY');
              let formattedYear = date.format('YYYY');

              if (chartId == 1358) {
                let formattedMonth12 = formattedYear + '-' + month;
                history.push({
                  pathname: '/AnalyzeData',
                  prevPath: window.location.pathname,
                  search: '?chartId=' + 'drillDown',
                  state: {
                    chartId: chartId,
                    techNo: techId,
                    techName: techId,
                    month_year: formattedMonth12,
                    isFrom: isFrom,
                    selectedToggle: selectedToggle,
                    drillDown: getChartDrillDown(chartId, isFrom),
                    chartName: getChartName(chartId),
                    category: getChartDataIndex(chartId, dataIndex, isFrom)
                  }
                });
              }
              if (chartId == 1352 || chartId == 1345 || chartId == 1363) {
                const currentDate = new Date();
                let year = currentDate.getFullYear();
                const monthYr = moment()
                  .month(monthYear[0])
                  .format('MM');

                const yearsOfSpecificMonth = years
                  .filter(date => {
                    const [, month] = date.split('-');
                    return month === monthYr.padStart(2, '0');
                  })
                  .map(date => parseInt(date.split('-')[0]));

                if (chartId == 1352) {
                  formattedMonth =
                    config.config.series[0].monthYear[config.dataPointIndex];
                  const date = new Date(formattedMonth);
                  date.setDate(date.getDate() + 6);
                  const newDate = date.toISOString().split('T')[0];
                  weekEnd = newDate;
                  weekStart = formattedMonth;
                } else {
                  formattedMonth =
                    yearsOfSpecificMonth[0] + '-' + month + '-' + monthYear[1];
                  const date = new Date(formattedMonth);
                  date.setDate(date.getDate() + 6);
                  const newDate = date.toISOString().split('T')[0];
                  weekEnd = newDate;
                  weekStart = formattedMonth;
                }

                var payType = getMatchingString(
                  config.globals.seriesNames[config.seriesIndex]
                );
              } else {
                let date = moment(monthYear[1], 'YY');
                let year = date.format('YYYY');
                formattedMonth = year + '-' + month;
              }
              let data = {
                type: 'techniciantrendingcharts',
                reportType: chartId,
                month_year: formattedMonth,
                chartName: chartTitle,
                techNo: techId,
                isFrom: isFrom,
                selectedToggle: selectedToggle,
                techName: techId,
                payType:
                  chartId == 1352 || chartId == 1363
                    ? payTypeSaved.includes(payType[0])
                      ? payTypeSaved
                      : // : payType[0] == 'W'
                        // ? ['W', 'F']
                        payType[0]
                    : payTypeSaved,
                monthSelected: formattedMonth,
                drilldownVal: 'techProd',
                weekEnd: weekEnd,
                weekStart: weekStart,
                techPageType: 'techSummary'
              };

              if (typeof parentCallback === 'undefined') {
                if (config.dataPointIndex >= 0) {
                  if (chartId == 1358) {
                    let formattedMonth12 = formattedYear + '-' + month;
                    history.push({
                      pathname: '/AnalyzeData',
                      prevPath: window.location.pathname,
                      search: '?chartId=' + 'drillDown',
                      state: {
                        chartId: chartId,
                        techNo: techId,
                        techName: techId,
                        month_year: formattedMonth12,
                        //   isFrom: isFrom,
                        drillDown: getChartDrillDown(chartId, isFrom),
                        chartName: getChartName(chartId),
                        category: getChartDataIndex(chartId, dataIndex, isFrom)
                      }
                    });
                  } else {
                    history.push({
                      pathname: '/TechnicianPerformance',
                      state: {
                        tabselection: 'nine',
                        isAdvisorCharts: false,
                        isEfficiencyCharts: false,
                        isTechnicianCharts: true,
                        month_year: data.month_year,
                        type: 'techniciantrendingcharts',
                        isFrom: 'techniciantrendingcharts',
                        prevPath:
                          window.location.pathname === '/GraphDetailsView'
                            ? window.location.pathname +
                              '?title=' +
                              chartId +
                              '?chartId=' +
                              chartId
                            : window.location.pathname,

                        // techNo: 'All[All]',
                        // techName: 'All[All]',
                        // payType:
                        //   chartId == 1352 || chartId == 1359 ? payType[0] : 'C',
                        // techNo:
                        //   session.menuSelected == 'Favorites'
                        //     ? 'All[All]'
                        //     : technician,
                        // techName:
                        //   session.menuSelected == 'Favorites'
                        //     ? 'All[All]'
                        //     : techId,
                        payType:
                          chartId == 1352 || chartId == 1363
                            ? payTypeSaved.includes(payType[0])
                              ? payTypeSaved
                              : // : payType[0] == 'W'
                                // ? ['W', 'F']
                                payType[0]
                            : payTypeSaved,
                        techNo: technician,
                        // session.menuSelected == 'Favorites'
                        //   ? 'All[All]'
                        //   : technician,
                        techName: techId,
                        // session.menuSelected == 'Favorites'
                        //   ? 'All[All]'
                        //   : techId,
                        chartType: 1,
                        chartId: chartId,
                        parent: 'techniciantrendingcharts',
                        drilldownVal: 'techProd',
                        weekEnd: weekEnd,
                        weekStart: weekStart,
                        techPageType: 'techSummary'
                      }
                    });
                  }
                }
              } else if (chartId != 1358) {
                parentCallback(data);
              }
            }
          }
        }
      },

      colors:
        chartId == 1352 || chartId == 1363
          ? ['#109618', '#DC3912', '#3366CC', '#993399']
          : chartId == 1358
          ? ['#109618', '#DC3912', '#3366CC']
          : chartId == 1345
          ? ['#5e2598']
          : chartId == 1347
          ? ['#5e2598']
          : ['#dd4477'],
      grid: {
        row: {
          colors: ['#f3f3f3', 'transparent'], // takes an array which will be repeated on columns
          opacity: 0.5
        }
      },
      markers: {
        size: 4,
        strokeWidth: 1,
        strokeOpacity: 0.7
      },
      dataLabels: {
        enabled: false
      },

      stroke: {
        curve: 'straight',
        width: 2.5
        // width: chartId == '1352' ? 1.8 : 2.5
      },
      yaxis: {
        title: {
          text:
            chartId == '1345' || chartId == '1352'
              ? 'Hours'
              : chartId == '1363'
              ? 'ROs'
              : chartId == '1358'
              ? 'RO Count'
              : chartId == '1348'
              ? 'Job Count'
              : 'Job Count%'
        },
        labels: {
          rotate: -10,
          style: {
            fontSize: '9px'
          },
          formatter: function(value) {
            if (chartId == 1347) {
              return (
                (
                  Math.round((value + Number.EPSILON) * 100) / 100
                ).toLocaleString() + ' %'
              );
            } else
              return (
                Math.round((value + Number.EPSILON) * 100) / 100
              ).toLocaleString();
          }
        }
      },
      xaxis: {
        tooltip: {
          enabled: false
        },
        title: {
          text:
            chartId == '1345' || chartId == '1352' || chartId == '1363'
              ? 'Week'
              : 'Month'
        },
        labels: {
          rotate: -45,
          rotateAlways:
            chartId == '1345' ||
            chartId == '1352' ||
            chartId == '1348' ||
            chartId == '1347' ||
            chartId == '1363'
              ? true
              : false
        }
      },

      legend: {
        itemMargin: {
          vertical: 10
        }
      },
      tooltip: {
        shared: false,
        intersect: true,
        x: {
          formatter: function(y, index) {
            return chartId == 1363 || chartId == 1352
              ? data[0].labels.map(c => moment(c).format("MMM-DD 'YY"))[y - 1] +
                  '<div style="font-size: 10px">' +
                  'Data For ' +
                  data[0].labels.map(c => moment(c).format('MMM DD'))[y - 1] +
                  ' to ' +
                  (y != 1
                    ? findPreviousDate(data[0].labels.map(c => c)[y - 2])
                    : // data[0].labels.map(c => moment(c).format('MMM DD '))[
                      //     y - 2
                      //   ]
                      // moment(localStorage.getItem('closedDate')).format(
                      //   'MMM DD '
                      // )) +
                      findAfterDate(data[0].labels.map(c => c)[y - 1])) +
                  '</div>'
              : y;
          }
        }
      }
    };

    setchartOptions(options);
  };
  const findPreviousDate = Date => {
    let givenDate = moment(Date);
    const previousDate = givenDate.clone().subtract(1, 'days');

    return previousDate.format('MMM DD');
  };
  const findAfterDate = Date => {
    let givenDate = moment(Date);
    const newDate = givenDate.add(6, 'days');

    return newDate.format('MMM DD');

    // const previousDate = givenDate.clone().subtract(1, 'days');

    // return previousDate.format('MMM DD');
  };
  const setactions = val => {
    if (val === 4) {
      history.push({
        pathname: '/GraphDetailsView',
        search: '?title=' + chartId + '?chartId=' + chartId,

        state: {
          tech: tech,
          tabSelection: mainTabSelection ? mainTabSelection : 'seven',
          selectedSubTab: tabSelection,
          SelectedLocation: window.location.pathname,
          techName: techName,
          parent: isFrom,
          chartType: chartId
        }
      });
    }
  };
  const getMatchingString = str => {
    const dataArray = ['All', 'C', 'W', 'I'];
    const matchingElements = dataArray.filter(element => {
      return element.charAt(0).toUpperCase() === str.charAt(0).toUpperCase();
    });
    return matchingElements;
  };
  return (
    <>
      <Card
        bordered={false}
        style={{
          //
          margin: isFrom == 'details'||window.location.pathname === '/MyFavorites'  ? 0 : 4,
          border: '1px solid #003d6b',
          borderRadius: window.location.pathname === '/MyFavorites' ? 0 : 4
        }}
      >
        {isTechLoading == true || isLoading == true ? (
          <Grid justify="center" className={classes.loaderGrid}>
            <CircularProgress size={60} />
          </Grid>
        ) : (
          <React.Fragment>
            <CardHeader
              style={
                typeof parentCallback == 'undefined'
                  ? { padding: 10, paddingLeft: 20 }
                  : { padding: 15 }
              }
              title={getChartName(chartId)}
              action={
                <MoreActions
                  removeFavourite={removeFavourite}
                  setActions={setactions}
                  chartId={chartId}
                  type={type}
                  chartPopup={chartPopup}
                  handleClose={handleClose}
                  // favoritesDisabled={true}
                ></MoreActions>
              }
              subheader={getSubHeader(chartId)}
            ></CardHeader>

            <Divider />
            <CardContent className={classes.paperContainer}>
              {(isTechLoading === false &&
                series.length === 0 &&
                ((data &&
                  data[0].datasets &&
                  data[0].datasets[0].data === null) ||
                  data === null)) ||
              (isTechLoading === false && isEmpty && series.length === 4) ||
              (isTechLoading === false && isEmpty && series.length === 3) ? (
                <Grid justify="center" className={classes.loaderGrid}>
                  <Typography
                    style={{
                      paddingLeft: '10px',

                      fontWeight: 'bold',
                      color: '#003d6b',
                      fontSize: 13
                    }}
                  >
                    No Data To Display
                  </Typography>
                </Grid>
              ) : (
                <ReactApexChart
                  options={chartoptions}
                  series={series}
                  type={
                    chartId == 1347 || chartId == 1348 || chartId == 1358
                      ? 'bar'
                      : 'line'
                  }
                  height={
                    window.location.pathname == '/MyFavorites'?
                    [1345,1352,1363,1358].includes(chartId)
                    ? 370
                    : 315
                      : isFrom == 'source_page'
                      ? 230
                      : typeof parentCallback == 'undefined'
                      ? type == 'popup'
                        ? 500
                        : 200
                      : type == 'popup'
                      ? 450
                      : 250
                  }
                />
              )}
            </CardContent>
          </React.Fragment>
        )}
      </Card>
      {/* )} */}
      <ChartDialog
        open={open}
        chartId={chartId}
        chartType="techBarChartRenderer"
        tech={tech}
        techName={techName}
        checkEmpty={checkEmpty}
        realm={realm}
        parentCallback={parentCallback}
        handlePopupClose={handleClose}
      />
    </>
  );
};
BarChartRendererPostgraphile.propTypes = {
  removeFav: PropTypes.func
};
export default BarChartRendererPostgraphile;
