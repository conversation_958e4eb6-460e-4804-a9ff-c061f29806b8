import {
  CircularProgress,
  Paper,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Divider
} from '@material-ui/core';
import MoreActions from 'src/components/MoreActions';
import React, { useEffect, useState } from 'react';

import { makeStyles } from '@material-ui/core/styles';
import ReactApexChart from 'react-apexcharts';
import {
  getTechEfficiencyByMonths,
  getTechnicianRevenue
} from 'src/utils/hasuraServices';
import { useHistory } from 'react-router';
import PropTypes from 'prop-types';
import { getComparisonMonths } from 'src/utils/Utils';
import { getSubHeader } from 'src/components/ViewGraphDetailsAction';
import ChartDialog from 'src/components/Dialog';
import moment from 'moment';
import clsx from 'clsx';
import 'src/styles.css';

const useStyles = makeStyles({
  formControl: {
    padding: 8
  },
  gridContainer: {
    padding: 8
  },
  paperContainer: {
    cursor: 'pointer',
    boxShadow: 'none',
    borderRadius: 5
  },
  container: {
    padding: 5
  },
  loaderGrid: {
    height: 300,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center'
  }
});

var lodash = require('lodash');
const ColumnRenderer = ({
  datatype,
  removeFav,
  month1,
  month2,
  handleClosePopup,
  tabSelection,
  mainTabSelection,
  parentCallback,
  callFrom,
  type,
  chartId,
  source,
  session,
  parent,
  techChartData
}) => {
  const classes = useStyles();
  const [open, setOpen] = useState(false);
  const [isLoading, setLoading] = useState(true);
  const [chartoptions, setchartOptions] = useState({});
  const [series, setSeries] = useState([]);
  const [techno, setTechno] = useState([]);
  const [charttype, setDataType] = useState([]);
  const [isFrom, setCallFrom] = useState([]);

  useEffect(() => {
    setLoading(true);
    console.log('isFrom', techChartData);
    if (parent == 'techefficiency') {
      if (techChartData.length > 0) {
        const chartType = getDataType(Number(chartId)); // 'workmix', 'elr', or 'jobcount'

        // const filterdData = techChartData.map(item => {
        //   const mon1Key = `${chartType}Mon1`;
        //   const mon2tKey = `${chartType}Mon2`;
        //   return {
        //     lbrtechno: item.lbrtechno,
        //     mon1: item[mon1Key],
        //     mon2: item[mon2tKey],
        //     storeId: item.storeId,
        //     teamId: item.teamId,
        //     teamName: item.teamName,
        //     techname: item.techname
        //   };
        // });
        const transformedData = transformComparisonData(techChartData);

        const chartData = transformedData[chartId];
        processData(chartData);
        //processData(filterdData);
      }
    } else {
      getDataforColumnChart();
    }
  }, [techChartData, datatype]);
  const history = useHistory();
  const removeFavourite = val => {
    removeFav(val);
  };
  const chartPopup = val => {
    // handleChartPopup(val);
    setOpen(true);
  };
  const transformComparisonData = workmixData => {
    const result = {};

    if (workmixData.length > 0) {
      const datasets = workmixData[0].datasets;

      // Find base datasets
      const lbrtechno = datasets.find(d => d.label === 'lbrtechno');
      const techname = datasets.find(d => d.label === 'techname');
      const techName = datasets.find(d => d.label === 'techname');
      const teamName = datasets.find(d => d.label === 'team_name');
      // Group datasets by chartId
      const chartGroups = {};
      datasets.forEach(dataset => {
        if (dataset.chartId) {
          if (!chartGroups[dataset.chartId]) {
            chartGroups[dataset.chartId] = {};
          }
          chartGroups[dataset.chartId][dataset.label] = dataset;
        }
      });

      // Transform each chartId group
      Object.keys(chartGroups).forEach(chartId => {
        const group = chartGroups[chartId];
        const mon1Dataset = Object.values(group).find(d =>
          d.label.includes('_mon1')
        );
        const mon2Dataset = Object.values(group).find(d =>
          d.label.includes('_mon2')
        );

        if (mon1Dataset && mon2Dataset) {
          result[chartId] = lbrtechno.data.map((techno, index) => ({
            lbrtechno: techno,
            mon1: mon1Dataset.data[index],
            mon2: mon2Dataset.data[index],

            teamId: techName.data[index], // Not available in current data
            teamName: teamName.data[index], // Not available in current data
            techname: techname.data[index]
          }));
        }
      });
    }

    return result;
  };
  const handleClose = () => {
    setOpen(false);
    handleClosePopup(1);
  };
  const getDataforColumnChart = () => {
    if (datatype == '') {
      month1 = month1 ? month1 : getComparisonMonths()[0];
      month2 = month2 ? month2 : getComparisonMonths()[1];
      datatype = getDataType(Number(chartId));
      callFrom = 1;
      setDataType(datatype);
      setCallFrom(callFrom);
    }
    if (
      datatype == 'revenue' ||
      datatype == 'soldhours' ||
      datatype == 'jobcount' ||
      datatype == 'jobperc'
    ) {
      getTechnicianRevenue(month1, month2, datatype, result => {
        if (
          result.data
            .statelessDbdPeopleMetricsTechnicianGetTechRevenueAndHoursByName
            .techRevenueAndHoursByNames
        ) {
          processData(
            result.data
              .statelessDbdPeopleMetricsTechnicianGetTechRevenueAndHoursByName
              .techRevenueAndHoursByNames
          );
        } else {
          setLoading(false);
        }
      });
    } else {
      getTechEfficiencyByMonths(month1, month2, datatype, result => {
        if (
          result.data
            .statelessDbdPeopleMetricsTechnicianGetTechEfficiencyMonthlyComparison
            .nodes
        ) {
          processData(
            result.data
              .statelessDbdPeopleMetricsTechnicianGetTechEfficiencyMonthlyComparison
              .nodes
          );
        } else {
          setLoading(false);
        }
      });
    }
  };

  const getGraphName = chartId => {
    var chartList = JSON.parse(global.localStorage.getItem('chart-master'));

    let chart = chartList.filter(item => item.chartId == chartId);
    return chart[0].chartName;
  };
  const getDataType = chartId => {
    var datatype = '';
    switch (chartId) {
      case 1264:
        datatype = 'revenue';
        break;
      case 1265:
        datatype = 'soldhours';
        break;
      case 1349:
        datatype = 'jobcount';
        break;
      case 1350:
        datatype = 'jobperc';
        break;
      case 1266:
        datatype = 'techefficiencyoverall';
        break;
      case 1267:
        datatype = 'estimatedtechefficiency';
        break;
      case 1268:
        datatype = 'nonzeroflatratehrsandnonzeroactualhrs';
        break;
      case 1269:
        datatype = 'nonzeroflatratehrsandzeroactualhrs';
        break;
      case 1270:
        datatype = 'zeroflatratehrsandnonzeroactualhrs';
        break;
      case 1271:
        datatype = 'zeroflatratehrsandzeroactualhrs';
        break;

      case 1272:
        datatype = 'jobcountflathrsandactualhrs';
        break;
      case 1273:
        datatype = 'jobcountflatratehrsandzeroactualhrs';
        break;
      case 1274:
        datatype = 'jobcountzeroflatratehrsandnonzeroactualhrs';
        break;
      case 1275:
        datatype = 'jobcountzeroflatratehrsandzeroactualhrs';
        break;
    }
    return datatype;
  };
  const processData = data => {
    data = data.filter(item => item.lbrtechno !== '' && item.lbrtechno != null);
    let oderedData;
    //if (chartId == '1264' || chartId == '1265')
    //oderedData = lodash.orderBy(data, 'mon1', 'desc');
    if (new Date(month1) > new Date(month2)) {
      oderedData = lodash.orderBy(
        data,
        function(data) {
          return new Number(data.mon1);
        },
        'desc'
      );
    } else {
      oderedData = lodash.orderBy(
        data,
        function(data) {
          return new Number(data.mon2);
        },
        'desc'
      );
    }

    var nullRemoved = oderedData.map(val => val.techname.replace('[', ' ['));

    var techNameArray = nullRemoved.filter(el => el != null);

    var lbrtechnoArray = oderedData.map(val => val.lbrtechno);

    const seriesData = [
      {
        name: moment(month1).format('MMM-YY'),
        data: oderedData.map(e => (e.mon1 > 0 ? e.mon1 : 0))
      },
      {
        name: moment(month2).format('MMM-YY'),
        data: oderedData.map(e => (e.mon2 > 0 ? e.mon2 : 0))
      }
    ];

    const options = {
      chart: {
        events: {
          dataPointSelection: function(event, chartContext, config) {
            var techno = lbrtechnoArray[config.dataPointIndex];
            let monthYear = config.w.globals.seriesNames[
              config.seriesIndex
            ].split('-');
            let date = moment(monthYear[1], 'YY');
            let year = date.format('YYYY');
            let month = moment()
              .month(monthYear[0])
              .format('MM');
            let targetMonthYear = year + '-' + month;
            let data = {
              type: 'techcomparison',
              person: techno,
              month: targetMonthYear,
              fstMonth: month1,
              sndMonth: month2,
              techName: config.w.globals.labels[config.dataPointIndex],
              chartId: chartId
              //reportType: getReportType(title)
            };
            if (typeof parentCallback == 'undefined') {
              history.push({
                pathname: '/TechnicianPerformance',
                state: {
                  tabselection: 'nine',
                  isAdvisorCharts: false,
                  isEfficiencyCharts: false,
                  isTechnicianCharts: true,
                  month_year: targetMonthYear,
                  type: 'techcomparison',
                  isFrom: 'techcomparison',
                  techNo: techno,
                  techName: config.w.globals.labels[config.dataPointIndex],
                  chartType: 1,
                  chartId: chartId,
                  parent: 'techcomparison'
                }
              });
            } else {
              parentCallback(data);
            }
          }
        },
        fontFamily: 'Roboto,Helvetica, Arial, sans-serif',
        toolbar: {
          show: false,
          autoSelected: 'zoom'
        }
      },
      dataLabels: {
        enabled: false
      },
      // title: {
      //   text: getGraphName(datatype),
      //   align: 'left'
      // },

      grid: {
        row: {
          colors: ['#f3f3f3', 'transparent'], // takes an array which will be repeated on columns
          opacity: 0.5
        }
      },
      theme: {
        palette: 'palette8'
      },

      yaxis: {
        labels: {
          style: {
            fontSize:
              window.location.pathname === '/MyFavorites' ||
              window.location.pathname !== '/MyFavorites'
                ? techNameArray && techNameArray.length > 45
                  ? '5px'
                  : '11px'
                : '11px'
          },
          formatter: function(value) {
            if (
              datatype == 'estimatedtechefficiency' ||
              datatype == 'techefficiencyoverall'
            ) {
              return (
                (
                  Math.round(value * 100 + Number.EPSILON) / 100
                ).toLocaleString() + ' %'
              );
            }
            if (getGraphName(chartId).includes('%') && datatype !== 'jobperc') {
              return (
                (
                  Math.round(value * 100 + Number.EPSILON) / 100
                ).toLocaleString() + ' %'
              );
            }
            if (
              datatype == 'revenue' ||
              datatype == 'soldhours' ||
              datatype == 'jobcount' ||
              datatype == 'jobperc'
            ) {
              return value;
            } else if (value == 'Infinity') {
              value = 100;
            }
            return (
              Math.round(value * 100 + Number.EPSILON) / 100
            ).toLocaleString();
          }
        }
      },
      xaxis: {
        labels: {
          style: {
            fontSize:
              window.location.pathname == '/MyFavorites' ? '11px' : '11px'
          },
          formatter: function(value) {
            if (datatype == 'revenue') {
              return '$' + value.toLocaleString();
            } else {
              return value;
            }
          }
        },
        title: {
          text:
            datatype == 'revenue'
              ? 'Revenue'
              : datatype == 'soldhours'
              ? 'Sold hours'
              : datatype == 'jobcount'
              ? 'Job count'
              : datatype == 'jobperc'
              ? 'Job count%'
              : 'Technician'
        },

        categories:
          datatype == 'revenue' ||
          datatype == 'soldhours' ||
          datatype == 'jobcount' ||
          datatype == 'jobperc'
            ? techNameArray
            : oderedData.map(val => val.lbrtechno)
      },
      colors: ['rgb(51,102,204)', 'rgb(220,57,18)'],
      plotOptions: {
        bar: {
          horizontal:
            datatype == 'revenue' ||
            datatype == 'soldhours' ||
            datatype == 'jobcount' ||
            datatype == 'jobperc'
              ? true
              : false
        }
      },
      tooltip: {
        shared: false,
        intersect: true,
        y: {
          formatter: function(value) {
            if (datatype == 'revenue') {
              return (
                '$' +
                value
                  .toFixed(2)
                  .toString()
                  .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
              );
            } else if (
              datatype == 'soldhours'
              //  || datatype == 'jobcount'
            ) {
              return value
                .toFixed(2)
                .toString()
                .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
            } else if (
              datatype == 'techefficiencyoverall' ||
              datatype == 'jobperc'
            ) {
              return (
                value
                  .toFixed(2)
                  .toString()
                  .replace(/\B(?=(\d{3})+(?!\d))/g, ',') + '%'
              );
            } else if (
              datatype == 'estimatedtechefficiency' ||
              datatype == 'nonzeroflatratehrsandnonzeroactualhrs' ||
              datatype == 'nonzeroflatratehrsandzeroactualhrs' ||
              datatype == 'zeroflatratehrsandzeroactualhrs' ||
              datatype == 'zeroflatratehrsandnonzeroactualhrs'
            ) {
              return (
                value
                  .toFixed(2)
                  .toString()
                  .replace(/\B(?=(\d{3})+(?!\d))/g, ',') + '%'
              );
            } else {
              return value;
            }
          }
        }
      },
      legend: {
        show: true,
        showForSingleSeries: false,
        showForNullSeries: true,
        showForZeroSeries: true,
        position: 'bottom',
        horizontalAlign: 'center',
        floating: false,
        fontSize: '14px',
        fontFamily: 'Roboto,Helvetica, Arial, sans-serif',
        fontWeight: 400,
        formatter: undefined,
        inverseOrder: false,
        width: undefined,
        height: undefined,
        tooltipHoverFormatter: undefined,
        customLegendItems: [],
        offsetX:
          chartId == 1264 ||
          chartId == 1265 ||
          chartId == 1349 ||
          chartId == 1350
            ? 120
            : 20,
        offsetY: 5
      }
    };

    setchartOptions(options);
    setSeries(seriesData);
    setTimeout(() => {
      setLoading(false);
    }, 200);
  };
  const setactions = val => {
    if (val === 4) {
      history.push({
        pathname: '/GraphDetailsView',
        search: '?title=' + chartId + '?chartId=' + chartId,
        state: {
          month1: month1 ? month1 : getComparisonMonths()[0],
          month2: month2 ? month2 : getComparisonMonths()[1],
          datatype: datatype ? datatype : charttype,
          tabSelection: mainTabSelection,
          selectedSubTab: tabSelection,
          SelectedLocation: window.location.pathname
        },
        SelectedLocation: window.location.pathname
      });
    }
  };
  return (
    <Paper
      square
      className={classes.paperContainer}
      style={
        window.location.pathname == '/MyFavorites'
          ? { margin: 0 }
          : { margin: 0 }
      }
    >
      {isLoading == true ? (
        <Grid justify="center" className={classes.loaderGrid}>
          <CircularProgress size={60} />
        </Grid>
      ) : (
        <Card
          bordered={false}
          style={{
            border: '1px solid #003d6b',
            borderRadius: window.location.pathname === '/MyFavorites' ? 0 : 4
          }}
          id={'chart-' + chartId}
        >
          <CardHeader
            style={
              typeof parentCallback == 'undefined'
                ? {
                    padding: 18,
                    paddingLeft: 20,
                    borderBottom: '1px solid #003d6b'
                  }
                : { padding: 15, borderBottom: '1px solid #003d6b' }
            }
            title={getGraphName(chartId)}
            action={
              <MoreActions
                removeFavourite={removeFavourite}
                setActions={setactions}
                chartId={chartId}
                //favoritesDisabled={true}
                month1={month1}
                month2={month2}
                type={type}
                chartPopup={chartPopup}
                handleClose={handleClose}
              ></MoreActions>
            }
            subheader={getSubHeader(chartId)}
          ></CardHeader>
          <Divider />
          <CardContent style={{ cursor: 'pointer' }}>
            <ReactApexChart
              options={chartoptions}
              series={series}
              type="bar"
              height={
                window.location.pathname == '/MyFavorites'
                  ? 370
                  : source == 'source_page'
                  ? '100%'
                  : localStorage.getItem('realm') == 'koonsofsilverspring' &&
                    (datatype == 'revenue' ||
                      datatype == 'soldhours' ||
                      datatype == 'jobcount' ||
                      datatype == 'jobperc') &&
                    callFrom == 0
                  ? 800
                  : (datatype == 'revenue' ||
                      datatype == 'soldhours' ||
                      datatype == 'jobcount' ||
                      datatype == 'jobperc') &&
                    callFrom == 0
                  ? 500
                  : (datatype == 'revenue' ||
                      charttype == 'revenue' ||
                      datatype == 'soldhours' ||
                      charttype == 'soldhours') &&
                    (callFrom == 1 || isFrom == 1)
                  ? 400
                  : type == 'popup' &&
                    localStorage.getItem('realm') == 'koonsofsilverspring'
                  ? 600
                  : type == 'popup'
                  ? 500
                  : window.location.pathname == '/GraphDetailsView'
                  ? 400
                  : 280
              }
            />
          </CardContent>
        </Card>
      )}
      <ChartDialog
        open={open}
        chartId={chartId}
        mon1={month1}
        mon2={month2}
        datatype={datatype}
        chartType="techComparison"
        realm={localStorage.getItem('realm')}
        parentCallback={parentCallback}
        handlePopupClose={handleClose}
        chartData={techChartData}
      />
    </Paper>
  );
};

function getGroupName(index) {
  let name = '';
  switch (index) {
    case 0:
      name = 'REPAIR';
      break;
    case 1:
      name = 'MAINTENANCE';
      break;
    case 2:
      name = 'COMPETITIVE';
      break;
    default:
      name = '';
      break;
  }
  return name;
}
ColumnRenderer.propTypes = {
  removeFav: PropTypes.func
};
export default ColumnRenderer;
