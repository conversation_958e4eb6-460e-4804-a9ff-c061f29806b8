import { QueryRenderer } from '@cubejs-client/react';
import { CircularProgress, Grid, Paper } from '@material-ui/core';
import moment from 'moment';
import React, { useState, useEffect } from 'react';
import ReactApexChart from 'react-apexcharts';
import cubejsApi from 'src/utils/cubeUtils';
import { Card, CardContent, CardHeader, Divider } from '@material-ui/core';
import {
  getChartDataIndex,
  getChartDrillDown,
  getChartName,
  getChartType,
  getLineChartType,
  GetyAxisRange,
  getSubHeader
} from 'src/components/ViewGraphDetailsAction';
import MoreActions from 'src/components/MoreActions';
import { useHistory } from 'react-router';
import { useSelector } from 'react-redux';
import { ReactSession } from 'react-client-session';

import ChartDialog from 'src/components/Dialog';
import { getDashboardGraphQuery } from 'src/components/DashboardGraphQuery';
import PropTypes from 'prop-types';
import { getChartsDataFromViews } from 'src/utils/hasuraServices';
import { makeStyles } from '@material-ui/core/styles';
import { setTime } from '@progress/kendo-react-dateinputs/dist/npm/utils';
const useStyles = makeStyles({
  loaderGrid: {
    height: 300,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center'
  }
});
const BarChartRenderer = ({
  chartId,
  removeFav,
  query,

  parentCallback,
  isFrom,
  handleClosePopup,
  type,
  realm,
  handleChartPopup,
  userhistory,
  chartData
}) => {
  const classes = useStyles();
  const [open, setOpen] = useState(false);
  const session = useSelector(state => state.session);
  let title = getChartName(chartId);

  const history = useHistory();
  const [queryState, queryChangedState] = useState({});

  const [isLoading, setLoading] = useState(true);
  const [chartoptions, setchartOptions] = useState({});
  const [series, setSeries] = useState([]);
  useEffect(() => {
    getDataforWorkmixCharts(chartId);
  }, [session.serviceAdvisor, chartId, session.storeSelected]);

  var chartList = JSON.parse(global.localStorage.getItem('chart-master'));
  let filteredResult = chartList.filter(item => item.chartId == chartId);
  let chartLoc;
  if (filteredResult[0].dbdName == 'Parts Workmix') {
    chartLoc = 'PartsWorkMixAnalysis';
  } else {
    chartLoc = 'LaborWorkMixAnalysis';
  }
  const removeFavourite = val => {
    removeFav(val);
  };
  const chartPopup = val => {
    localStorage.setItem('popup', true);
    handleChartPopup(val);
    setOpen(true);
  };
  const handleClose = () => {
    localStorage.setItem('popup', false);
    setOpen(false);

    handleClosePopup(1);
  };
  const getDataforWorkmixCharts = chartId => {
    setLoading(true);

    if (chartData) {
      let data = JSON.parse(chartData[0].jsonData)[0].datasets;
      const extractedData = data.filter(i => i.chartId == chartId);
      const result = extractedData;
      const updatedData = result.map(item => {
        return {
          name: item.label,
          data: item.data ? item.data : [],
          month: JSON.parse(chartData[0].jsonData)[0].labels
            ? JSON.parse(chartData[0].jsonData)[0].labels.map(item =>
                moment(item).format('MMM ’YY')
              )
            : []
        };
      });

      const seriesData = updateNegativeValues(updatedData);
      setSeries(seriesData);

      const options = generateChartOptions(seriesData, chartId);
      setchartOptions(options);
      setLoading(false);
    } else {
      getChartsDataFromViews(session.serviceAdvisor, chartId, callback => {
        if (callback) {
          setLoading(false);

          let data = JSON.parse(callback[0].jsonData)[0].datasets;
          const extractedData = data.filter(i => i.chartId == chartId);
          const result = extractedData;
          const updatedData = result.map(item => {
            return {
              name: item.label,
              data: item.data ? item.data : [],
              month: JSON.parse(callback[0].jsonData)[0].labels
                ? JSON.parse(callback[0].jsonData)[0].labels.map(item =>
                    moment(item).format('MMM ’YY')
                  )
                : []
            };
          });
          const seriesData = updateNegativeValues(updatedData);
          setSeries(seriesData);

          const options = generateChartOptions(seriesData, chartId);
          setchartOptions(options);
        } else {
          setLoading(false);
        }
      });
    }
  };

  const generateChartOptions = (seriesData, chartId) => {
    return {
      chart: {
        id: 'chart' + chartId,
        events: {
          dataPointSelection: function(event, chartContext, config) {
            let data = {
              type: 'workmixcharts',
              reportType: getReportType(title)
            };
            if (
              typeof parentCallback == 'undefined' &&
              session.serviceAdvisor.includes('All') == true
            ) {
              history.push({
                pathname: '/' + chartLoc,
                state: {
                  chartId: chartId,
                  tabselection: 'three',
                  reportType: getReportType(getChartName(chartId)),
                  isAdvisorCharts: false,
                  isEfficiencyCharts: true,
                  isFrom: 'workmixcharts',
                  isParts: false,
                  isTechnicianCharts: false,
                  isWorkMixRouting: true
                }
              });
            } else if (session.serviceAdvisor.includes('All') == false) {
              let queryMonth =
                config.w.config.xaxis.categories[config.dataPointIndex];
              const date1 = moment(queryMonth.split(' ’')[1], 'YY');
              const year = date1.format('YYYY');
              let monthYear =
                year +
                '-' +
                moment()
                  .month(queryMonth.split(' ’')[0])
                  .format('MM');

              let prevPath =
                userhistory &&
                userhistory.location &&
                userhistory.location.prevPath
                  ? userhistory.location.prevPath
                  : userhistory && userhistory.location == undefined
                  ? userhistory
                  : '';
              history.push({
                pathname: '/ServiceAdvisorPerformance',
                state: {
                  tabselection: 'eight',
                  parent: 'workmixAdvisor',
                  selectedSubTab: 'three',
                  month_year: monthYear,
                  chartLoc: chartLoc,
                  prevPath: prevPath,
                  currentPath: window.location.pathname,
                  chartId: chartId
                }
              });
            } else {
              parentCallback(data);
            }
          }
        },
        fontFamily: 'Roboto,Helvetica, Arial, sans-serif',
        toolbar: {
          show: false,
          autoSelected: 'zoom'
        }
      },
      dataLabels: {
        enabled: false
      },
      grid: {
        // colors: ['#008FFB', '#FEB019', '#4CAF50'],
        row: {
          colors: ['#f3f3f3', 'transparent'], // takes an array which will be repeated on rows
          opacity: 0.5
        }
      },
      theme: {
        palette: 'palette8' // upto palette10
      },
      yaxis: {
        labels: {
          formatter: function(value) {
            if (title.includes('Mix')) {
              return Number.isInteger(value)
                ? value + '%'
                : value.toFixed(1) + '%';
            } else if (title.includes('Profit')) {
              return Number.isInteger(value)
                ? value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') + '%'
                : value.toFixed(1) + '%';
            } else if (
              title.includes('Cost') ||
              title.includes('Sale') ||
              title.includes('ELR')
            ) {
              return Number.isInteger(value)
                ? ' $' + value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                : ' $' + value.toFixed(1);
            } else if (title.includes('Job')) {
              return Number.isInteger(value)
                ? value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                : value.toFixed(1);
            } else if (title.includes('Markup')) {
              return Number.isInteger(value) ? value : value.toFixed(1);
            } else
              return Number.isInteger(value)
                ? value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                : value.toFixed(1);
          }
        }
      },
      xaxis: {
        labels: {
          rotate: -45,
          rotateAlways: true
        },
        categories: seriesData[0]?.month || []
      },
      tooltip: {
        shared: false,
        intersect: true,
        x: {
          formatter: function(y, index) {
            return y.replace(/ ’/g, '-');
          }
        },
        y: {
          formatter: function(y) {
            if (
              (typeof y != 'undefined' && title == 'Gross Profit%') ||
              title == 'Gross Profit %'
            ) {
              return y.toFixed(1) + '%';
            } else if (
              typeof y != 'undefined' &&
              (title == 'Labor Sale' ||
                title == 'ELR' ||
                title == 'Parts Sale' ||
                title == 'Parts Cost')
            ) {
              return (
                ' $' +
                y
                  .toFixed(2)
                  .toString()
                  .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
              );
            } else if (typeof y != 'undefined' && title == 'Parts Markup') {
              return y.toFixed(4);
            } else if (typeof y != 'undefined' && title.includes('Mix')) {
              return (
                y
                  .toFixed(2)
                  .toString()
                  .replace(/\B(?=(\d{3})+(?!\d))/g, ',') + '%'
              );
            } else if (title == 'Sold Hours') {
              return y
                .toFixed(2)
                .toString()
                .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
            } else {
              return y.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
            }
          }
        }
      }
    };
  };

  const updateNegativeValues = dataSet => {
    return dataSet.map(({ name, data, month }) => {
      const updatedData = data.map(value =>
        parseFloat(value) < 0 ? '0.00' : value
      );

      return {
        name,
        data: updatedData,
        month
      };
    });
  };

  const getReportType = title => {
    let reportType = '';
    if (title.includes('Gross')) {
      reportType = 'grossprofit';
    } else if (title.includes('ELR')) {
      reportType = 'elr';
    } else if (title.includes('Markup')) {
      reportType = 'markup';
    } else if (title.includes('Work Mix')) {
      reportType = 'workmix';
    } else if (title.includes('Cost')) {
      reportType = 'Cost';
    } else if (title.includes('Sold Hours')) {
      reportType = 'soldhours';
    } else if (title.includes('Job Count')) {
      reportType = 'jobcount';
    } else if (title.includes('Sale')) {
      reportType = 'Sales';
    }
    setTimeout(() => {
      ReactSession.set('selectedReport', reportType);
    }, 1000);
    return reportType;
  };
  const setactions = val => {
    let prevPath =
      userhistory && userhistory.location && userhistory.location.prevPath
        ? userhistory.location.prevPath
        : userhistory && userhistory.location == undefined
        ? userhistory
        : '';
    if (val === 4) {
      history.push({
        pathname: '/GraphDetailsView',
        search: '?chartId=' + chartId,
        SelectedLocation: window.location.pathname,
        isFrom: prevPath ? 'opportunity' : 'workmixAdvisor',
        userhistory:
          userhistory && userhistory.location && userhistory.location.prevPath
            ? userhistory.location.prevPath
            : userhistory && userhistory.location == undefined
            ? userhistory
            : ''
      });
    }
  };
  return (
    <>
      {/* {isLoading == true ? (
        <Grid justify="center" className={classes.loaderGrid}>
          <CircularProgress size={60} />
        </Grid>
      ) : ( */}
      <Card
        bordered={false}
        style={
          window.location.pathname == '/MyFavorites'
            ? type == 'popup'
              ? {
                  height: 600,
                  margin: 0,
                  borderRadius: 0,
                  border: '1px solid #003d6b'
                }
              : {
                  height: 396,
                  margin: 0,
                  borderRadius: 0,
                  border: '1px solid #003d6b'
                }
            : type == 'popup'
            ? {
                height: 600,
                margin: 0,
                borderRadius: 0,
                border: '1px solid #003d6b'
              }
            : { margin: 0, borderRadius: 0, border: '1px solid #003d6b' }
        }
      >
        {isLoading == true ? (
          <Grid justify="center" className={classes.loaderGrid}>
            <CircularProgress size={60} />
          </Grid>
        ) : (
          <React.Fragment>
            <CardHeader
              style={
                typeof parentCallback == 'undefined'
                  ? { padding: 10, paddingLeft: 20 }
                  : { padding: 15 }
              }
              title={getChartName(chartId)}
              action={
                <MoreActions
                  removeFavourite={removeFavourite}
                  setActions={setactions}
                  chartId={chartId}
                  type={type}
                  chartPopup={chartPopup}
                  handleClose={handleClose}
                  // favoritesDisabled={true}
                ></MoreActions>
              }
              subheader={getSubHeader(chartId)}
            ></CardHeader>

            <Divider />
            <CardContent
              style={
                localStorage.getItem('popup') == 'true'
                  ? { cursor: 'pointer', height: 380 }
                  : { cursor: 'pointer' }
              }
            >
              <ReactApexChart
                options={chartoptions}
                series={series}
                type="bar"
                height={
                  window.location.pathname == '/MyFavorites'
                    ? '120%'
                    : isFrom == 'source_page'
                    ? 230
                    : typeof parentCallback == 'undefined'
                    ? type == 'popup'
                      ? 500
                      : 200
                    : type == 'popup'
                    ? 450
                    : 250
                }
              />
            </CardContent>
          </React.Fragment>
        )}
      </Card>
      {/* )} */}
      <ChartDialog
        open={open}
        chartId={chartId}
        chartType="bar"
        realm={realm}
        parentCallback={parentCallback}
        handlePopupClose={handleClose}
      />
    </>
  );
};
BarChartRenderer.propTypes = {
  removeFav: PropTypes.func
};
export default BarChartRenderer;
