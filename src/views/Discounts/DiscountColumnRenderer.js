import {
  CircularProgress,
  Grid,
  Paper,
  Card,
  CardContent,
  CardHeader,
  Divider
} from '@material-ui/core';
import React, { useEffect, useState } from 'react';
import { getDiscountROPercByServiceAdvisor } from 'src/utils/hasuraServices';
import {
  getChartName,
  getChartDrillDown,
  getSubHeader
} from 'src/components/ViewGraphDetailsAction';
import { getColorScheme, getComparisonMonths } from 'src/utils/Utils';
import ReactApexChart from 'react-apexcharts';
import { useHistory } from 'react-router';
import MoreActions from 'src/components/MoreActions';

import ChartDialog from 'src/components/Dialog';
import PropTypes from 'prop-types';
import moment from 'moment';
import { useDispatch, useSelector } from 'react-redux';
import { isEmpty } from 'validate.js';

var lodash = require('lodash');

const DiscountColumnRenderer = ({
  datatype,
  removeFav,
  month1,
  month2,
  type,
  chartId,
  handleClosePopup,
  isFrom,
  parentCallback,
  tabSelection
}) => {
  const [open, setOpen] = useState(false);
  const [isLoading, setLoading] = useState(true);
  const [chartoptions, setchartOptions] = useState({});
  const [series, setSeries] = useState([]);
  const session = useSelector(state => state.session);
  const [isEmpty, setEmpty] = useState([]);
  const history = useHistory();
  useEffect(() => {
    setLoading(true);
    getDataforColumnChart();
  }, [session.serviceadvisor, session.storeSelected]);
  useEffect(() => {
    setLoading(true);
    getDataforColumnChart();
  }, [month1, month2, datatype, session.serviceadvisor]);
  const removeFavourite = val => {
    removeFav(val);
  };
  // if (typeof chartId == 'undefined') {
  //   chartId = history.location.search
  //     .split('?chartId=')
  //     .pop()
  //     .split('?')[0];
  // }
  const chartPopup = val => {
    // handleChartPopup(val);
    setOpen(true);
  };
  const handleClose = () => {
    setOpen(false);
    handleClosePopup(1);
  };
  const getDataforColumnChart = () => {
    if (datatype == '') {
      month1 = month1 ? month1 : getComparisonMonths()[0];
      month2 = month2 ? month2 : getComparisonMonths()[1];
      datatype = getDataType(Number(chartId));
    }
    getDiscountROPercByServiceAdvisor(month1, month2, datatype, result => {
      if (
        result.data
          .statelessDbdDiscountsGetDiscountServiceAdvisorsMonthlyComparison
          .nodes
      ) {
        var resultArr =
          result.data
            .statelessDbdDiscountsGetDiscountServiceAdvisorsMonthlyComparison
            .nodes;

        processData(resultArr, datatype);
      } else {
        setLoading(false);
      }
    });
  };
  const getDataType = chartId => {
    var datatype = '';
    switch (chartId) {
      case 1124:
        datatype = 'ropercentage';
        break;
      case 1126:
        datatype = 'jobpercentage';
        break;
    }
    return datatype;
  };

  const setactions = val => {
    if (val === 4) {
      history.push({
        pathname: '/GraphDetailsView',
        search: '?chartId=' + chartId,
        state: {
          month1: month1,
          month2: month2,
          datatype: datatype,
          SelectedLocation: window.location.pathname,
          tabSelection: window.location.pathname == '/MyFavorites' ? '' : 'two'
        },
        isFrom: isFrom,
        SelectedLocation: window.location.pathname
      });
    }
  };
  const processData = data => {
    let orderedData = lodash.orderBy(data, 'mon1', 'desc');
    orderedData = orderedData.filter(function(val) {
      return val['serviceadvisor'] !== '';
    });
    let dataType = datatype;
    const seriesData = [
      {
        name: moment(month2).format('MMM-YY'),
        data: orderedData.map(e => e.mon2)
      },
      {
        name: moment(month1).format('MMM-YY'),
        data: orderedData.map(e => e.mon1)
      }
    ];
    const options = {
      chart: {
        fontFamily: 'Roboto,Helvetica, Arial, sans-serif',
        toolbar: {
          show: false,
          autoSelected: 'zoom'
        },
        events: {
          dataPointSelection: function(event, chartContext, config) {
            var selectedIndex = config.dataPointIndex;
            var seriesIndex = config.seriesIndex;
            var selectedAdvisor =
              chartContext.w.config.xaxis.categories[selectedIndex];
            let monthYear = chartContext.w.config.series[
              seriesIndex
            ].name.split('-');
            let date = moment(monthYear[1], 'YY');
            let year = date.format('YYYY');
            let month = moment()
              .month(monthYear[0])
              .format('MM');
            var selectedMonth = year + '-' + month;
            history.push({
              pathname: '/AnalyzeData',
              prevPath:
                window.location.pathname == '/GraphDetailsView'
                  ? window.location.pathname + '?chartId=' + chartId
                  : window.location.pathname,
              state: {
                chartId: chartId,
                x: selectedAdvisor,
                y: selectedMonth,
                monthYearComparison: [month1, month2],
                drillDown: getChartDrillDown(Number(chartId), 'dis'),
                chartName: getChartName(chartId),
                tabSelection: 'two',
                datatype: datatype
              }
            });
          }
        }
      },
      dataLabels: {
        enabled: false
      },
      title: {
        align: 'left'
      },
      grid: {
        row: {
          colors: ['#f3f3f3', 'transparent'], // takes an array which will be repeated on columns
          opacity: 0.5
        }
      },
      theme: {
        palette: 'palette8' // upto palette10
      },
      plotOptions: {
        bar: {
          horizontal: true
        },
        series: {
          states: {
            inactive: {
              enabled: false // Disable the inactive state to prevent opacity changes
            }
          }
        }
      },
      noData: {
        text: 'No data to display',
        align: 'center',
        verticalAlign: 'middle',
        style: {
          fontSize: '14px',
          fontWeight: 'bold',
          fontFamily: 'Roboto'
        }
      },
      colors: ['rgb(51,102,204)', 'rgb(220,57,18)'],
      xaxis: {
        labels: {
          formatter: function(value) {
            if (getChartName(chartId).includes('%')) {
              return Number.isInteger(value)
                ? value + ' %'
                : value.toFixed(2) + ' %';
            } else return value;
          }
        },
        categories: orderedData.map(val => val.advisorName)
      },
      yaxis: {
        title: {
          text: 'Service Advisor'
        }
      },
      tooltip: {
        shared: false,
        intersect: true,
        y: {
          formatter: function(y) {
            if (typeof y != 'undefined' && y != null) {
              return y.toFixed(2) + '%';
            }
          }
        }
      },
      legend: {
        onItemHover: {
          highlightDataSeries: false // Disable hover effect on legend items
        },
        // onItemClick: {
        //   toggleDataSeries: false // Disable the default toggle behavior
        // },
        events: {
          legendItemClick: function(
            event,
            chartContext,
            { seriesIndex, config }
          ) {
            event.preventDefault(); // Prevent the default behavior of toggling series
          }
        }
      }
    };

    setchartOptions(options);

    setEmpty(seriesData.every(element => element.data.length === 0));
    setSeries(seriesData);
    setTimeout(() => {
      setLoading(false);
    }, 200);
  };

  return (
    <Paper
      square
      style={{
        margin: window.location.pathname == '/MyFavorites' ? 0 : 4,
        height: window.location.pathname == '/MyFavorites' ? 380 : undefined
      }}
    >
      {isLoading == true ? (
        <Grid
          justify="center"
          style={{
            height: 300,
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center'
          }}
        >
          <CircularProgress size={60} />
        </Grid>
      ) : (
        <Card
          bordered={false}
          style={{
            height: '100%',
            textAlign: 'left',
            cursor: 'pointer',
            borderRadius: 0,
            border: '1px solid #003d6b'
          }}
        >
          <CardHeader
            title={getChartName(chartId)}
            action={
              <MoreActions
                removeFavourite={removeFavourite}
                setActions={setactions}
                chartId={chartId}
                month1={month1}
                month2={month2}
                type={type}
                chartPopup={chartPopup}
                handleClose={handleClose}
                // favoritesDisabled={true}
              ></MoreActions>
            }
            subheader={getSubHeader(chartId)}
            style={{ borderBottom: '1px solid #003d6b' }}
          ></CardHeader>

          <Divider />
          <CardContent>
            <ReactApexChart
              options={chartoptions}
              series={series}
              type="bar"
              height={
                isFrom == 'source_page' && chartId != 1126 && chartId != 1124
                  ? window.location.pathname == '/MyFavorites'
                    ? 255
                    : 215
                  : 280
              }
            />
          </CardContent>
        </Card>
      )}
      <ChartDialog
        open={open}
        chartId={chartId}
        mon1={month1}
        mon2={month2}
        datatype={datatype}
        chartType="discounts"
        realm={localStorage.getItem('realm')}
        handlePopupClose={handleClose}
      />
    </Paper>
  );
};
DiscountColumnRenderer.propTypes = {
  removeFav: PropTypes.func
};
export default DiscountColumnRenderer;
