import {
  FormControl,
  Grid,
  InputLabel,
  MenuItem,
  Paper,
  Select
} from '@material-ui/core';
import { makeStyles } from '@material-ui/core/styles';
import React, { useEffect, useState, useRef } from 'react';
import { getLastThreeYears, getLast13Months } from 'src/utils/Utils';
import Discount from '../AnalyzeData/ComparisonCharts/ColumnRenderer';
import DiscountColumnRenderer from './DiscountColumnRenderer';
import moment from 'moment';
import clsx from 'clsx';
import 'src/styles.css';
import { useDispatch, useSelector } from 'react-redux';
var lodash = require('lodash');

const useStyles = makeStyles({
  formControl: {
    minWidth: 120,
    maxWidth: 300
  },
  gridContainer: {
    display: 'flex'
  },
  paperContainer: {
    cursor: 'pointer',
    boxShadow: 'none',
    borderRadius: 5
  },
  container: {
    padding: '8px 0px'
  },
  alertInfo: {
    fontSize: 14,
    fontWeight: 'bold',
    width: 'inherit'
  }
});

const ComparisonChartsGrid = ({
  mon1,
  mon2,
  parentCallback,
  removeFav,
  isFrom,
  tabSelection,
  setResetDashboard,
  resetReport
}) => {
  const classes = useStyles();
  const [queryMonth, setQueryMonth] = useState(mon2);
  const [queryMonth2, setQueryMonth2] = useState(mon1);
  const [reset, setReset] = useState(resetReport);
  const [charts] = useState([
    'discountserviceadv',
    'discountserviceadvOpcat',
    'ropercentage',
    'jobpercentage'
  ]);
  const session = useSelector(state => state.session);
  useEffect(() => {
    if (reset != resetReport) {
      handleResetData();
    }
  });

  const handleClosePopup = value => {
    console.log('state===handleClosePopup');
  };
  const handleMonthchange = event => {
    setQueryMonth(event.target.value);
  };
  const handleMonthchange2 = event => {
    setQueryMonth2(event.target.value);
  };
  const handleResetData = () => {
    setReset(false);
    setResetDashboard();
    setQueryMonth(mon1);
    setQueryMonth2(mon2);
  };
  var chartList = JSON.parse(global.localStorage.getItem('chart-master'));

  let filteredResult = chartList.filter(
    item =>
      item.dbdName == 'Discounts-Month Comparison' &&
      item.parentId == item.chartId
  );
  let orderedData = lodash.orderBy(filteredResult, 'sort', 'asc');

  return (
    <Grid container className={classes.container}>
      <Grid item xs={12}>
        <Paper square classes={{ root: classes.gridContainer }}>
          <FormControl
            variant="outlined"
            margin="dense"
            className={classes.formControl}
            style={{ marginLeft: '17px' }}
          >
            <InputLabel htmlFor="outlined-age-native-simple" margin="dense">
              Month1
            </InputLabel>

            <Select
              variant="outlined"
              label="Group By"
              hintText="hiii"
              name="group-by-type"
              value={queryMonth}
              onChange={handleMonthchange}
            >
              {localStorage.getItem('versionFlag') == 'TRUE'
                ? getLastThreeYears().map(val => (
                    <MenuItem value={val}>
                      {moment(val).format('MMM-YY')}
                    </MenuItem>
                  ))
                : getLast13Months().map(val => (
                    <MenuItem value={val}>
                      {moment(val).format('MMM-YY')}
                    </MenuItem>
                  ))}
            </Select>
          </FormControl>
          <FormControl
            variant="outlined"
            margin="dense"
            className={classes.formControl}
            style={{ marginLeft: '17px' }}
          >
            <InputLabel htmlFor="outlined-age-native-simple" margin="dense">
              Month2
            </InputLabel>
            <Select
              variant="outlined"
              label="Group By"
              hintText="hiii"
              name="group-by-type"
              value={queryMonth2}
              onChange={handleMonthchange2}
            >
              {localStorage.getItem('versionFlag') == 'TRUE'
                ? getLastThreeYears().map(val => (
                    <MenuItem value={val}>
                      {moment(val).format('MMM-YY')}
                    </MenuItem>
                  ))
                : getLast13Months().map(val => (
                    <MenuItem value={val}>
                      {moment(val).format('MMM-YY')}
                    </MenuItem>
                  ))}
            </Select>
          </FormControl>
        </Paper>
      </Grid>

      <Grid container className={classes.container} spacing={12}>
        {charts.map((val, index) => {
          if (
            val === 'discountserviceadv' ||
            val === 'discountserviceadvOpcat'
          ) {
            return (
              <Grid
                item
                xs={12}
                justify="flex-start"
                id={'chart-' + orderedData[index].chartId}
                className={clsx(classes.container, 'diagram-section')}
              >
                <Discount
                  isPartsCharts={false}
                  datatype={val}
                  month1={queryMonth2}
                  month2={queryMonth}
                  handleClosePopup={handleClosePopup}
                  parentCallback={parentCallback}
                  chartId={orderedData[index].chartId}
                  removeFav={removeFav}
                  isFrom={isFrom}
                  tabSelection={tabSelection}
                  session={session}
                />
              </Grid>
            );
          } else {
            return (
              <Grid
                item
                xs={6}
                justify="flex-start"
                id={'chart-' + orderedData[index].chartId}
                className={clsx(classes.container, 'diagram-section')}
              >6666
                {/* <DiscountColumnRenderer
                  datatype={val}
                  month1={queryMonth2}
                  month2={queryMonth}
                  handleClosePopup={handleClosePopup}
                  chartId={orderedData[index].chartId}
                  removeFav={removeFav}
                  isFrom={isFrom}
                  tabSelection={tabSelection}
                /> */}
              </Grid>
            );
          }
        })}
      </Grid>
    </Grid>
  );
};

export default ComparisonChartsGrid;
